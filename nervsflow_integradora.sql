create or replace procedure nervsflow_integradora (
   p_dados_json in clob,
   p_retorno    out varchar2
) as
    -- Variáveis para controle de sequência
   v_numtransvenda     number;
   v_numtransent       number;
   v_numtransitem      number;

    -- Variáveis para dados do cabeçalho
   v_codfilial         varchar2(20);
   v_codcli            number;
   v_codfornec         number;
   v_codoper           varchar2(2);

    -- Variáveis para valores do JSON - CABEÇALHO
   v_chavenfe          varchar2(44);
   v_cgc_filial        varchar2(14);
   v_cgc_cliente       varchar2(14);
   v_serie             varchar2(3);
   v_numnota           number;
   v_nf_tipo           number;
   v_tpnf              varchar2(1);
   v_nat_op            varchar2(60);
   v_dtemissao         varchar2(19);
   v_valor_nf          number;
   v_pedido            varchar2(20);
   v_finnfe            number;
   v_tpimp             number;
   v_modelo            varchar2(2);
   v_cstat             number;
   v_nprot             varchar2(20);
   v_uf_emitente       varchar2(2);
   v_uf_destinatario   varchar2(2);
   v_refnfe            varchar2(44);
   v_tipofj            varchar2(1);
   v_codcob            varchar2(10);
   v_infcpl            varchar2(500);
   v_cst_pis_cofins    varchar2(10);
   v_cfop              number;
   v_xped              varchar2(20);
   v_infadprod         varchar2(500);
   v_codrca            number;
   v_codpraca          number;
   v_baseicst          number;
   v_codfunc           number;
   v_proxnumtrans      number;
   v_vipidevol         number;
   v_cgcent            varchar2(20);
   v_coddevol          number;
   v_codcontfor        number;
   v_is_devolucao      varchar2(1); -- 'S' para devolução, 'N' para outras entradas
   v_numtransvenda_ref number; -- NUMTRANSVENDA da nota original
   v_numnota_ref       number; -- NUMNOTA da nota original
   v_proxnumcred       number; -- PROXNUMCRED para PCCRECLI
   v_custofin          number; -- Custo financeiro
   v_custoreal         number; -- Custo real
   v_custorep          number; -- Custo reposição
   v_custocont         number; -- Custo contábil
   v_codcontcli        number; -- Código contábil do cliente

    -- Variáveis para endereço destinatário
   v_xlgr              varchar2(100);
   v_nro               varchar2(10);
   v_xbairro           varchar2(60);
   v_xmun              varchar2(60);
   v_xpais             varchar2(60);
   v_cpais             varchar2(4);
   v_cmun              varchar2(7);
   v_cep               varchar2(8);
   v_indfinal          number;
   v_indiedest         number;

    -- Variáveis para totais da NFe
   v_vltotal           number;
   v_vprod             number;
   v_vicms             number;
   v_vbc               number;
   v_vpis              number;
   v_vcofins           number;
   v_vipi              number;
   v_vfrete            number;
   v_voutros           number;
   v_vdesc             number;
   v_vst               number;

    -- Variáveis para totalizadores
   v_total_custofin    number := 0;
   v_total_custoreal   number := 0;
   v_total_custorep    number := 0;
   v_total_custocont   number := 0;

    -- Variáveis de controle
   v_primeiro_registro boolean := true;
   v_data_atual        date := sysdate;

   -- Cursor para processar o JSON
   cursor c_json is
   select json_value(column_value,
           '$.chavenfe') as chavenfe,
          json_value(column_value,
                     '$.serie') as serie,
          json_value(column_value,
                     '$.numnota') as numnota,
          json_value(column_value,
                     '$.tpnf') as tpnf,
          json_value(column_value,
                     '$.finnfe') as finnfe,
          json_value(column_value,
                     '$.tpimp') as tpimp,
          json_value(column_value,
                     '$.modelo') as modelo,
          json_value(column_value,
                     '$.natop') as natop,
          json_value(column_value,
                     '$.dtemissao') as dtemissao,
          json_value(column_value,
                     '$.cstat') as cstat,
          json_value(column_value,
                     '$.nprot') as nprot,
          json_value(column_value,
                     '$.cnpjemitente') as cnpjemitente,
          json_value(column_value,
                     '$.ufemitente') as ufemitente,
          json_value(column_value,
                     '$.cnpjdestinatario') as cnpjdestinatario,
          json_value(column_value,
                     '$.ufdestinatario') as ufdestinatario,
          json_value(column_value,
                     '$.refnfe') as refnfe,
          json_value(column_value,
                     '$.tipofj') as tipofj,
          json_value(column_value,
                     '$.codcob') as codcob,
          json_value(column_value,
                     '$.infcpl') as infcpl,
          json_value(column_value,
                     '$.cst_pis_cofins') as cst_pis_cofins,
          json_value(column_value,
                     '$.xped') as xped,
          json_value(column_value,
                     '$.infadprod') as infadprod,
          json_value(column_value,
                     '$.codrca') as codrca,
          json_value(column_value,
                     '$.codpraca') as codpraca,
          json_value(column_value,
                     '$.baseicst') as baseicst,
          json_value(column_value,
                     '$.codfunc') as codfunc,
          json_value(column_value,
                     '$.proxnumtrans') as proxnumtrans,
          json_value(column_value,
                     '$.vipidevol') as vipidevol,
          json_value(column_value,
                     '$.cgcent') as cgcent,
          json_value(column_value,
                     '$.coddevol') as coddevol,
          json_value(column_value,
                     '$.codcontfor') as codcontfor,
          json_value(column_value,
                     '$.proxnumtransent') as proxnumtransent,
          json_value(column_value,
                     '$.is_devolucao') as is_devolucao,
          json_value(column_value,
                     '$.refnfe') as refnfe_dev,
          json_value(column_value,
                     '$.proxnumcred') as proxnumcred,
         -- Dados de endereço
          json_value(column_value,
                     '$.xlgr') as xlgr,
          json_value(column_value,
                     '$.nro') as nro,
          json_value(column_value,
                     '$.xbairro') as xbairro,
          json_value(column_value,
                     '$.xmun') as xmun,
          json_value(column_value,
                     '$.xpais') as xpais,
          json_value(column_value,
                     '$.cpais') as cpais,
          json_value(column_value,
                     '$.cmun') as cmun,
          json_value(column_value,
                     '$.cep') as cep,
          json_value(column_value,
                     '$.indfinal') as indfinal,
          json_value(column_value,
                     '$.indiedest') as indiedest,
         -- Totais da NFe
          json_value(column_value,
                     '$.vltotal') as vltotal,
          json_value(column_value,
                     '$.vprod') as vprod,
          json_value(column_value,
                     '$.vicms') as vicms,
          json_value(column_value,
                     '$.vbc') as vbc,
          json_value(column_value,
                     '$.vpis') as vpis,
          json_value(column_value,
                     '$.vcofins') as vcofins,
          json_value(column_value,
                     '$.vipi') as vipi,
          json_value(column_value,
                     '$.vfrete') as vfrete,
          json_value(column_value,
                     '$.voutros') as voutros,
          json_value(column_value,
                     '$.vdesc') as vdesc,
          json_value(column_value,
                     '$.vst') as vst,
         -- Dados dos itens
          json_value(column_value,
                     '$.nitem') as nitem,
          json_value(column_value,
                     '$.cprod') as cprod,
          json_value(column_value,
                     '$.cean') as cean,
          json_value(column_value,
                     '$.xprod') as xprod,
          json_value(column_value,
                     '$.ncm') as ncm,
          json_value(column_value,
                     '$.cfop') as cfop,
          json_value(column_value,
                     '$.ucom') as ucom,
          json_value(column_value,
                     '$.qcom') as qcom,
          json_value(column_value,
                     '$.vuncom') as vuncom,
          json_value(column_value,
                     '$.vprod_item') as vprod_item,
          json_value(column_value,
                     '$.vdesc_item') as vdesc_item,
          json_value(column_value,
                     '$.vfrete_item') as vfrete_item,
          json_value(column_value,
                     '$.voutro_item') as voutro_item,
          json_value(column_value,
                     '$.punitcont') as punitcont,
          json_value(column_value,
                     '$.picms') as picms,
          json_value(column_value,
                     '$.cst') as cst,
          json_value(column_value,
                     '$.vicms_item') as vicms_item,
          json_value(column_value,
                     '$.vbc_item') as vbc_item,
          json_value(column_value,
                     '$.ppis') as ppis,
          json_value(column_value,
                     '$.vpis_item') as vpis_item,
          json_value(column_value,
                     '$.vbcpis') as vbcpis,
          json_value(column_value,
                     '$.pcofins') as pcofins,
          json_value(column_value,
                     '$.vcofins_item') as vcofins_item,
          json_value(column_value,
                     '$.vbccofins') as vbccofins,
          json_value(column_value,
                     '$.pipi') as pipi,
          json_value(column_value,
                     '$.vipi_item') as vipi_item,
          json_value(column_value,
                     '$.vbcipi') as vbcipi,
          json_value(column_value,
                     '$.orig') as orig,
          json_value(column_value,
                     '$.cst_pis_cofins') as cst_pis_cofins_item,
          json_value(column_value,
                     '$.codcest') as codcest,
          json_value(column_value,
                     '$.pst') as pst,
          json_value(column_value,
                     '$.vbcufdest') as vbcufdest,
          json_value(column_value,
                     '$.picmsufdest') as picmsufdest,
          json_value(column_value,
                     '$.picmsinter') as picmsinter,
          json_value(column_value,
                     '$.picmsinterpart') as picmsinterpart,
          json_value(column_value,
                     '$.vlicmspartdest') as vlicmspartdest,
          json_value(column_value,
                     '$.vlicmspartrem') as vlicmspartrem,
          json_value(column_value,
                     '$.pfcpufdest') as pfcpufdest,
          json_value(column_value,
                     '$.vlfcppart') as vlfcppart
     from
      json_table ( p_dados_json,'$[0][*]'
         columns (
            column_value clob format json path '$'
         )
      );

begin
    -- Inicializar processamento
   p_retorno := 'SUCCESS';

   -- Debug: mostrar JSON recebido
   dbms_output.put_line('JSON recebido (primeiros 500 chars): '
                        || substr(
      p_dados_json,
      1,
      500
   ));

   -- Debug: testar extração direta do JSON
   declare
      v_test_cnpj varchar2(20);
   begin
      select json_value(p_dados_json,
           '$[0][0].cnpjemitente')
        into v_test_cnpj
        from dual;
      dbms_output.put_line('=== TESTE EXTRAÇÃO DIRETA ===');
      dbms_output.put_line('CNPJ Emitente direto: ['
                           || nvl(
         v_test_cnpj,
         'NULL'
      )
                           || ']');
   exception
      when others then
         dbms_output.put_line('Erro na extração direta: ' || sqlerrm);
   end;

   -- =====================================================
   -- CONTROLE TRANSACIONAL - INÍCIO
   -- =====================================================
   savepoint inicio_processamento;

   -- Obter números de sequência no início para garantir consistência
   begin
      -- Para notas de saída: obter NUMTRANSVENDA
      select proxnumtransvenda
        into v_numtransvenda
        from pcconsum
       where rownum = 1
      for update;

      -- Para notas de entrada: obter NUMTRANSENT
      select proxnumtransent
        into v_numtransent
        from pcconsum
       where rownum = 1
      for update;

      dbms_output.put_line('=== NÚMEROS DE TRANSAÇÃO OBTIDOS ===');
      dbms_output.put_line('NUMTRANSVENDA: ' || v_numtransvenda);
      dbms_output.put_line('NUMTRANSENT: ' || v_numtransent);
   exception
      when others then
         rollback to inicio_processamento;
         p_retorno := 'ERROR: Falha ao obter números de sequência: ' || sqlerrm;
         raise_application_error(
            -20001,
            p_retorno
         );
   end;

   -- Obter dados do primeiro registro para validações iniciais
   declare
      v_first_rec    c_json%rowtype;
      v_cursor_count number := 0;
   begin
      open c_json;
      fetch c_json into v_first_rec;
      if c_json%found then
         v_cursor_count := 1;
      end if;
      close c_json;
      dbms_output.put_line('=== DEBUG CURSOR ===');
      dbms_output.put_line('Registros encontrados no cursor: ' || v_cursor_count);
      if v_cursor_count > 0 then
         dbms_output.put_line('JSON do primeiro registro: '
                              || substr(
            v_first_rec.chavenfe
            || '|'
            || v_first_rec.cnpjemitente
            || '|'
            || v_first_rec.cnpjdestinatario,
            1,
            200
         ));
      end if;

      -- Debug: mostrar dados do primeiro registro
      dbms_output.put_line('=== DEBUG PRIMEIRO REGISTRO ===');
      dbms_output.put_line('chavenfe: ['
                           || nvl(
         v_first_rec.chavenfe,
         'NULL'
      )
                           || ']');
      dbms_output.put_line('cnpjemitente: ['
                           || nvl(
         v_first_rec.cnpjemitente,
         'NULL'
      )
                           || ']');
      dbms_output.put_line('cnpjdestinatario: ['
                           || nvl(
         v_first_rec.cnpjdestinatario,
         'NULL'
      )
                           || ']');

      -- Extrair chave da NFe para validação
      v_chavenfe := v_first_rec.chavenfe;
   exception
      when others then
         rollback to inicio_processamento;
         p_retorno := 'ERROR: Falha ao obter dados do primeiro registro: ' || sqlerrm;
         raise_application_error(
            -20001,
            p_retorno
         );
   end;

   -- Validar se a NFe já foi processada (ANTES do loop)
   declare
      v_count_saida   number := 0;
      v_count_entrada number := 0;
   begin
      -- Verificar se já existe em PCNFSAID (notas de saída) - apenas não canceladas
      select count(*)
        into v_count_saida
        from pcnfsaid
       where chavenfe = v_chavenfe
         and dtcancel is null;

      -- Verificar se já existe em PCNFENT (notas de entrada) - apenas não canceladas
      select count(*)
        into v_count_entrada
        from pcnfent
       where chavenfe = v_chavenfe
         and dtcancel is null;

      -- Se encontrou em qualquer uma das tabelas, a NFe já foi processada
      if v_count_saida > 0
      or v_count_entrada > 0 then
         -- Debug: mostrar detalhes dos registros encontrados
         dbms_output.put_line('❌ NFe duplicada encontrada:');
         dbms_output.put_line('   - PCNFSAID: '
                              || v_count_saida
                              || ' registro(s)');
         dbms_output.put_line('   - PCNFENT: '
                              || v_count_entrada
                              || ' registro(s)');

         -- Mostrar detalhes dos registros em PCNFSAID
         if v_count_saida > 0 then
            for rec_debug in (
               select numtransvenda,
                      numnota,
                      dtsaida,
                      dtcancel,
                      situacaonfe
                 from pcnfsaid
                where chavenfe = v_chavenfe
            ) loop
               dbms_output.put_line('   PCNFSAID: NUMTRANSVENDA='
                                    || rec_debug.numtransvenda
                                    || ', NUMNOTA='
                                    || rec_debug.numnota
                                    || ', DTSAIDA='
                                    || to_char(
                  rec_debug.dtsaida,
                  'DD/MM/YYYY'
               )
                                    || ', DTCANCEL='
                                    || nvl(
                  to_char(
                     rec_debug.dtcancel,
                     'DD/MM/YYYY'
                  ),
                  'NULL'
               )
                                    || ', SITUACAO='
                                    || rec_debug.situacaonfe);
            end loop;
         end if;

         -- Mostrar detalhes dos registros em PCNFENT
         if v_count_entrada > 0 then
            for rec_debug in (
               select numtransent,
                      numnota,
                      dtent,
                      dtcancel,
                      situacaonfe
                 from pcnfent
                where chavenfe = v_chavenfe
            ) loop
               dbms_output.put_line('   PCNFENT: NUMTRANSENT='
                                    || rec_debug.numtransent
                                    || ', NUMNOTA='
                                    || rec_debug.numnota
                                    || ', DTENT='
                                    || to_char(
                  rec_debug.dtent,
                  'DD/MM/YYYY'
               )
                                    || ', DTCANCEL='
                                    || nvl(
                  to_char(
                     rec_debug.dtcancel,
                     'DD/MM/YYYY'
                  ),
                  'NULL'
               )
                                    || ', SITUACAO='
                                    || rec_debug.situacaonfe);
            end loop;
         end if;

         rollback to inicio_processamento;
         p_retorno := 'ERROR: NFe já processada anteriormente: '
                      || v_chavenfe
                      || ' (Saída: '
                      || v_count_saida
                      || ', Entrada: '
                      || v_count_entrada
                      || ')';
         raise_application_error(
            -20001,
            p_retorno
         );
      end if;

      dbms_output.put_line('✅ Validação NFe duplicada: OK (não encontrada em PCNFSAID nem PCNFENT)');
   exception
      when others then
         rollback to inicio_processamento;
         p_retorno := 'ERROR: Falha na validação de NFe duplicada: ' || sqlerrm;
         raise_application_error(
            -20001,
            p_retorno
         );
   end;

   -- Processar cada registro do JSON
   for rec in c_json loop

      -- No primeiro registro, processar o cabeçalho da NF
      if v_primeiro_registro then
         -- Converter dados do cabeçalho
         v_chavenfe := rec.chavenfe;
         v_serie := rec.serie;
         v_numnota := to_number ( rec.numnota );
         v_nf_tipo := to_number ( rec.tpnf );
         v_tpnf := rec.tpnf;
         v_finnfe := to_number ( rec.finnfe );
         v_tpimp := to_number ( rec.tpimp );
         v_modelo := rec.modelo;
         v_nat_op := rec.natop;

         -- Converter data de emissão (formato: DD/MM/YYYY HH24:MI:SS)
         begin
            v_dtemissao := rec.dtemissao;
         exception
            when others then
               v_dtemissao := to_char(
                  sysdate,
                  'DD/MM/YYYY HH24:MI:SS'
               );
         end;

         v_cstat := to_number ( rec.cstat );
         v_nprot := rec.nprot;
         v_cgc_filial := rec.cnpjemitente;
         v_uf_emitente := rec.ufemitente;
         v_cgc_cliente := rec.cnpjdestinatario;
         v_uf_destinatario := rec.ufdestinatario;

         -- Debug: mostrar valores extraídos do JSON
         dbms_output.put_line('=== DEBUG EXTRAÇÃO JSON ===');
         dbms_output.put_line('CNPJ Emitente extraído: ['
                              || nvl(
            v_cgc_filial,
            'NULL'
         )
                              || ']');
         dbms_output.put_line('CNPJ Destinatário extraído: ['
                              || nvl(
            v_cgc_cliente,
            'NULL'
         )
                              || ']');
         dbms_output.put_line('Chave NFe: ['
                              || nvl(
            v_chavenfe,
            'NULL'
         )
                              || ']');
         v_refnfe := rec.refnfe;
         v_tipofj := rec.tipofj;
         v_codcob := nvl(
            rec.codcob,
            'CML'
         );
         v_infcpl := rec.infcpl;
         v_cst_pis_cofins := nvl(
            rec.cst_pis_cofins,
            '00'
         );
         v_cfop := to_number ( nvl(
            rec.cfop,
            5102
         ) );
         v_xped := rec.xped;
         v_infadprod := rec.infadprod;
         v_codrca := to_number ( nvl(
            rec.codrca,
            802
         ) );
         v_codpraca := to_number ( nvl(
            rec.codpraca,
            331
         ) );
         v_baseicst := to_number ( nvl(
            rec.baseicst,
            0
         ) );
         v_codfunc := to_number ( nvl(
            rec.codfunc,
            1
         ) );
         v_proxnumtrans := to_number ( nvl(
            rec.proxnumtrans,
            1
         ) );
         v_vipidevol := to_number ( nvl(
            rec.vipidevol,
            0
         ) );
         -- Para notas de entrada, o CGC do emitente é o fornecedor
         v_cgcent :=
            case
               when v_tpnf = '0' then
                  v_cgc_filial
               else v_cgc_cliente
            end;
         v_coddevol := to_number ( nvl(
            rec.coddevol,
            1
         ) );
         v_codcontfor := to_number ( nvl(
            rec.codcontfor,
            1
         ) );
         -- v_numtransent já foi obtido no início da procedure
         v_is_devolucao := nvl(
            rec.is_devolucao,
            'N'
         );

         -- Debug: verificar se é devolução
         dbms_output.put_line('🔍 DEBUG DEVOLUÇÃO:');
         dbms_output.put_line('  - is_devolucao (JSON): '
                              || nvl(
            rec.is_devolucao,
            'NULL'
         ));
         dbms_output.put_line('  - v_is_devolucao: ' || v_is_devolucao);
         dbms_output.put_line('  - natOp: '
                              || nvl(
            rec.natop,
            'NULL'
         ));
         dbms_output.put_line('  - refNFe: '
                              || nvl(
            rec.refnfe,
            'NULL'
         ));
         v_refnfe := nvl(
            rec.refnfe,
            rec.refnfe_dev
         );
         v_proxnumcred := to_number ( nvl(
            rec.proxnumcred,
            1
         ) );

         -- Inicializar custos (serão atualizados depois com dados do PCEST)
         v_custofin := 0;
         v_custoreal := 0;
         v_custorep := 0;
         v_custocont := 0;

         -- Dados de endereço
         v_xlgr := nvl(
            rec.xlgr,
            'NAO INFORMADO'
         );
         v_nro := nvl(
            rec.nro,
            'S/N'
         );
         v_xbairro := nvl(
            rec.xbairro,
            'NAO INFORMADO'
         );
         v_xmun := nvl(
            rec.xmun,
            'NAO INFORMADO'
         );
         v_xpais := nvl(
            rec.xpais,
            'BRASIL'
         );
         v_cpais := nvl(
            rec.cpais,
            '1058'
         ); -- Brasil como padrão
         v_cmun := nvl(
            rec.cmun,
            '0000000'
         );
         v_cep := nvl(
            rec.cep,
            '00000000'
         );
         v_indfinal := to_number ( nvl(
            rec.indfinal,
            '1'
         ) ); -- 1 = Consumidor final
         v_indiedest := to_number ( nvl(
            rec.indiedest,
            '9'
         ) ); -- 9 = Não contribuinte

         -- Totais da NFe
         v_vltotal := to_number ( rec.vltotal );
         v_vprod := to_number ( rec.vprod );
         v_vicms := to_number ( rec.vicms );
         v_vbc := to_number ( rec.vbc );
         v_vpis := to_number ( rec.vpis );
         v_vcofins := to_number ( rec.vcofins );
         v_vipi := to_number ( rec.vipi );
         v_vfrete := to_number ( rec.vfrete );
         v_voutros := to_number ( rec.voutros );
         v_vdesc := to_number ( rec.vdesc );
         v_vst := to_number ( nvl(
            rec.vst,
            0
         ) );

         -- Números de transação já foram obtidos no início
         -- v_numtransvenda e v_numtransent já estão disponíveis

         -- Buscar código da filial
         begin
            -- Debug: mostrar CNPJ que está sendo buscado
            dbms_output.put_line('Buscando filial para CNPJ: ' || v_cgc_filial);
            select codigo
              into v_codfilial
              from pcfilial
             where regexp_replace(
                  cgc,
                  '[^0-9]',
                  ''
               ) = regexp_replace(
                  v_cgc_filial,
                  '[^0-9]',
                  ''
               )
               and rownum = 1;

            dbms_output.put_line('Filial encontrada: ' || v_codfilial);
         exception
            when no_data_found then
               -- Mostrar CNPJs disponíveis para debug
               dbms_output.put_line('CNPJs disponíveis na PCFILIAL:');
               for rec_debug in (
                  select cgc
                    from pcfilial
                   where rownum <= 5
               ) loop
                  dbms_output.put_line('  - ' || rec_debug.cgc);
               end loop;

               rollback to inicio_processamento;
               p_retorno := 'ERROR: Filial não encontrada para o CNPJ ' || v_cgc_filial;
               raise_application_error(
                  -20001,
                  p_retorno
               );
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao buscar filial: ' || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;

         -- Buscar código do cliente
         begin
            -- Debug: mostrar CNPJ do cliente que está sendo buscado
            dbms_output.put_line('Buscando cliente para CNPJ/CPF: ' || v_cgc_cliente);
            select codcli
              into v_codcli
              from pcclient
             where regexp_replace(
                  cgcent,
                  '[^0-9]',
                  ''
               ) = regexp_replace(
                  v_cgc_cliente,
                  '[^0-9]',
                  ''
               )
               and rownum = 1;

            -- Validar se o código do cliente foi encontrado
            if v_codcli is null then
               p_retorno := 'ERROR: Cliente com CNPJ/CPF '
                            || v_cgc_cliente
                            || ' encontrado mas CODCLI é nulo';
               raise_application_error(
                  -20001,
                  p_retorno
               );
            end if;

            dbms_output.put_line('Cliente encontrado: ' || v_codcli);
         exception
            when no_data_found then
               dbms_output.put_line('Cliente não encontrado para CNPJ/CPF: ' || v_cgc_cliente);
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Cliente não encontrado para o CNPJ/CPF ' || v_cgc_cliente;
               raise_application_error(
                  -20001,
                  p_retorno
               );
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao buscar cliente: ' || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;

         -- Determinar código de operação baseado no tipo da nota
         v_codoper :=
            case
               when v_tpnf = '0' then
                  -- Notas de entrada
                  case
                     when v_is_devolucao = 'S' then
                        'ED' -- TIPODESCARGA = '6'
                     else 'ER' -- TIPODESCARGA = 'R'
                  end
               else
                  -- Notas de saída
                  case
                     when v_tpnf = '1' then
                        'S' -- TIPOVENDA = '1'
                     else 'SR' -- TIPOVENDA = 'SR'
                  end
            end;

         v_primeiro_registro := false;
      end if;

      -- Processar dados dos itens
      declare
         v_count               number;
         -- Variáveis locais para armazenar valores do JSON dos itens
         v_nitem               number;
         v_cprod               varchar2(60);
         v_cean                varchar2(60);
         v_xprod               varchar2(500);
         v_ncm                 varchar2(20);
         v_cfop_item           number;
         v_ucom                varchar2(10);
         v_qcom                number;
         v_vuncom              number;
         v_vprod_item          number;
         v_vdesc_item          number;
         v_vfrete_item         number;
         v_voutro_item         number;
         v_punitcont           number;
         v_picms               number;
         v_cst                 varchar2(10);
         v_vicms_item          number;
         v_vbc_item            number;
         v_ppis                number;
         v_vpis_item           number;
         v_vbcpis              number;
         v_pcofins             number;
         v_vcofins_item        number;
         v_vbccofins           number;
         v_pipi                number;
         v_vipi_item           number;
         v_vbcipi              number;
         v_orig                varchar2(1);
         v_cst_pis_cofins_item varchar2(10);
         v_codcest             varchar2(20);
         v_pst                 number;
         v_vbcufdest           number;
         v_picmsufdest         number;
         v_picmsinter          number;
         v_picmsinterpart      number;
         v_vlicmspartdest      number;
         v_vlicmspartrem       number;
         v_pfcpufdest          number;
         v_vlfcppart           number;
      begin
         -- Converter dados dos itens
         v_nitem := to_number ( rec.nitem );
         v_cprod := rec.cprod;
         v_cean := nvl(
            rec.cean,
            '0'
         );
         v_xprod := rec.xprod;
         v_ncm := rec.ncm;
         v_cfop_item := to_number ( rec.cfop );
         v_ucom := rec.ucom;
         v_qcom := to_number ( rec.qcom );
         v_vuncom := to_number ( rec.vuncom );
         v_vprod_item := to_number ( rec.vprod_item );
         v_vdesc_item := to_number ( rec.vdesc_item );
         v_vfrete_item := to_number ( rec.vfrete_item );
         v_voutro_item := to_number ( rec.voutro_item );
         -- PUNITCONT já vem calculado corretamente do JavaScript
         v_punitcont := to_number ( rec.punitcont );
         v_picms := to_number ( rec.picms );
         v_cst := rec.cst;
         v_vicms_item := to_number ( rec.vicms_item );
         v_vbc_item := to_number ( rec.vbc_item );
         v_ppis := to_number ( rec.ppis );
         v_vpis_item := to_number ( rec.vpis_item );
         v_vbcpis := to_number ( rec.vbcpis );
         v_pcofins := to_number ( rec.pcofins );
         v_vcofins_item := to_number ( rec.vcofins_item );
         v_vbccofins := to_number ( rec.vbccofins );
         v_pipi := to_number ( rec.pipi );
         v_vipi_item := to_number ( rec.vipi_item );
         v_vbcipi := to_number ( rec.vbcipi );
         v_orig := rec.orig;
         v_cst_pis_cofins_item := nvl(
            rec.cst_pis_cofins_item,
            rec.cst_pis_cofins
         );
         v_codcest := rec.codcest;
         v_pst := to_number ( rec.pst );
         v_vbcufdest := to_number ( rec.vbcufdest );
         v_picmsufdest := to_number ( rec.picmsufdest );
         v_picmsinter := to_number ( rec.picmsinter );
         v_picmsinterpart := to_number ( rec.picmsinterpart );
         v_vlicmspartdest := to_number ( rec.vlicmspartdest );
         v_vlicmspartrem := to_number ( rec.vlicmspartrem );
         v_pfcpufdest := to_number ( rec.pfcpufdest );
         v_vlfcppart := to_number ( rec.vlfcppart );

         -- Validar se o produto existe
         select count(*)
           into v_count
           from pcprodut
          where codprod = v_cprod;

         if v_count = 0 then
            p_retorno := 'ERROR: Produto não encontrado: ' || v_cprod;
            raise_application_error(
               -20001,
               p_retorno
            );
         end if;

         -- Obter próximo número de transação do item
         select dfseq_pcmovcomple.nextval
           into v_numtransitem
           from dual;

         -- Inserir movimentação do item (PCMOV)
         begin
            insert into pcmov (
               numtransvenda,
               numtransitem,
               numseq,
               codoper,
               numnota,
               vloutrasdesp,
               dtmovlog,
               dtmov,
               codsittribpiscofins,
               codplpag,
               numcar,
               codcli,
               codfilial,
               codfilialnf,
               status,
               numtransent,
               codfornec,
               percdesc,
               vlbonific,
               codfilialretira,
               geraicmslivrofiscal,
               compraconsignado,
               movestoquecontabil,
               movestoquegerencial,
               codprod,
               codauxiliar,
               nbm,
               codfiscal,
               unidade,
               qt,
               qtcont,
               punit,
               punitcont,
               vldesconto,
               percicm,
               sittribut,
               vlfrete,
               baseicms,
               vlpis,
               vlbasepiscofins,
               vlcofins,
               vlipi,
               percipi,
               vlbaseipi,
               ptabela,
               codusur,
               percfrete,
               vlcredpis,
               perpis,
               percofins,
               custofin,
               custoreal,
               custorep,
               custocont,
               calccredipi
            ) values ( v_numtransvenda,
                       v_numtransitem,
                       v_nitem,
                       v_codoper,
                       v_numnota,
                       nvl(
                          v_voutro_item,
                          0
                       ),
                       to_date(v_dtemissao,
                               'DD/MM/YYYY HH24:MI:SS'),
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'),
                       v_cst_pis_cofins_item,
                       1, -- CODPLPAG fixo
                       0, -- NUMCAR fixo (0 para aparecer na rotina 410)
                       v_codcli,
                       v_codfilial,
                       v_codfilial,
                       case
                          when v_tpnf = '0' then
                             'AB'
                          else
                             'AB'
                       end, -- STATUS
                       case
                          when v_tpnf = '0' then
                             v_numtransent
                          else
                             null
                       end, -- NUMTRANSENT
                       case
                          when v_tpnf = '0' then
                             v_codfornec
                          else
                             null
                       end, -- CODFORNEC
                       0, -- PERCDESC fixo
                       0, -- VLBONIFIC fixo
                       case
                          when v_tpnf = '1' then
                             v_codfilial
                          else
                             null
                       end, -- CODFILIALRETIRA
                       'S', -- GERAICMSLIVROFISCAL fixo
                       'N', -- COMPRACONSIGNADO fixo
                       'S', -- MOVESTOQUECONTABIL fixo
                       'S', -- MOVESTOQUEGERENCIAL fixo
                       v_cprod,
                       v_cean,
                       v_ncm,
                       v_cfop_item,
                       v_ucom,
                       v_qcom,
                       v_qcom,
                       v_vuncom,
                       v_punitcont,
                       v_vdesc_item,
                       v_picms,
                       v_cst,
                       v_vfrete_item,
                       v_vbc_item,
                       v_vpis_item,
                       v_vbcpis,
                       v_vcofins_item,
                       v_vipi_item,
                       v_pipi,
                       v_vbcipi,
                       v_vuncom,
                       v_codrca, -- CODUSUR do XML
                       case
                          when nvl(
                             v_vfrete_item,
                             0
                          ) = 0 then
                             0
                          else
                             round(
                                (v_vfrete_item / v_vuncom) * 100,
                                2
                             )
                       end,
                       case
                          when v_tpnf = '0' then
                             v_vpis_item / v_qcom -- Entrada: crédito de PIS
                          else
                             v_vpis_item / v_qcom -- Saída: débito de PIS
                       end,
                       v_ppis, -- perpis
                       v_pcofins, -- percofins
                       v_custofin, -- Será atualizado depois com dados do PCEST
                       v_custoreal, -- Será atualizado depois com dados do PCEST
                       v_custorep, -- Será atualizado depois com dados do PCEST
                       v_custocont, -- Será atualizado depois com dados do PCEST
                       case
                          when v_tpnf = '0'
                             and v_is_devolucao = 'S' then
                             'S'
                          else
                             'N'
                       end -- CALCCREDIPI
            );
         exception
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao inserir PCMOV item '
                            || v_nitem
                            || ': '
                            || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;

         -- Inserir complemento da movimentação (PCMOVCOMPLE)
         begin
            insert into pcmovcomple (
               numtransitem,
               dtregistro,
               aliqicms1ret,
               vlbasepartdest,
               aliqinternadest,
               aliqinterorigpart,
               percprovpart,
               vlicmspartdest,
               vlicmspartrem,
               aliqfcp,
               vlfcppart,
               numchaveexp,
               codprodgnre,
               eancodprod,
               codtribpiscofins,
               nitemxml,
               codcest,
               origmerctrib,
               vlbasefrete,
               vlbaseoutros
            ) values ( v_numtransitem,
                       to_date(v_dtemissao,
                               'DD/MM/YYYY HH24:MI:SS'),
                       v_pst,
                       v_vbcufdest,
                       v_picmsufdest,
                       v_picmsinter,
                       v_picmsinterpart,
                       v_vlicmspartdest,
                       v_vlicmspartrem,
                       v_pfcpufdest,
                       v_vlfcppart,
                       v_chavenfe,
                       v_cprod,
                       v_cean,
                       7, -- CODTRIBPISCOFINS fixo
                       v_nitem,
                       v_codcest,
                       v_orig, -- origmerctrib (código origem da mercadoria)
                       case
                          when v_tpnf = '0'
                             and v_is_devolucao = 'S' then
                             0
                          else
                             v_vfrete_item
                       end, -- VLBASEFRETE (0 para devoluções)
                       case
                          when v_tpnf = '0'
                             and v_is_devolucao = 'S' then
                             0
                          else
                             v_voutro_item
                       end -- VLBASEOUTROS (0 para devoluções)
            );
         exception
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao inserir PCMOVCOMPLE item '
                            || v_nitem
                            || ': '
                            || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;

         -- Calcular CMV e custos do produto baseado em tributação por UF ou Região
         declare
            v_custofin_final  number := 0;
            v_custoreal_final number := 0;
            v_custorep_final  number := 0;
            v_custocont_final number := 0;
            v_tribuf          varchar2(1);
            v_codst_final     number := 0;
            v_codicmtab       number := 0;
            v_codicmtabpf     number := 0;
            v_txvenda         number := 0;
            v_cmv_calculado   number := 0;
            v_codstpartilha   number := null;
         begin
            -- Verificar parâmetro de tributação por UF usando função PARAMFILIAL
            begin
               select paramfilial.obtercomovarchar2('CON_USATRIBUTACAOPORUF')
                 into v_tribuf
                 from dual;
            exception
               when others then
                  v_tribuf := 'N';
            end;

            -- Buscar TXVENDA do PCCONSUM
            begin
               select nvl(
                  txvenda,
                  0
               )
                 into v_txvenda
                 from pcconsum
                where rownum = 1;
            exception
               when no_data_found then
                  v_txvenda := 0;
            end;

            if v_tribuf = 'S' then
               -- TRIBUTAÇÃO POR UF
               begin
                  select t.codst,
                         c.codicmtab,
                         c.codicmtabpf,
                         e.custofin,
                         e.custoreal,
                         e.custorep,
                         e.custocont,
                         ( ( ( v_vuncom * c.codicmtab ) / 100 ) + ( ( v_vuncom * v_txvenda ) / 100 ) + e.custofin )
                    into
                     v_codst_final,
                     v_codicmtab,
                     v_codicmtabpf,
                     v_custofin_final,
                     v_custoreal_final,
                     v_custorep_final,
                     v_custocont_final,
                     v_cmv_calculado
                    from pctabtrib t,
                         pctribut c,
                         pcest e
                   where t.codst = c.codst
                     and e.codfilial = t.codfilialnf
                     and e.codprod = t.codprod
                     and t.codfilialnf = v_codfilial
                     and t.ufdestino = v_uf_destinatario
                     and e.codprod = v_cprod
                     and rownum = 1;
               exception
                  when no_data_found then
                     -- Se não encontrar por UF, usar custos base da PCEST
                     begin
                        select nvl(
                           custofin,
                           0
                        ),
                               nvl(
                                  custoreal,
                                  0
                               ),
                               nvl(
                                  custorep,
                                  0
                               ),
                               nvl(
                                  custocont,
                                  0
                               )
                          into
                           v_custofin_final,
                           v_custoreal_final,
                           v_custorep_final,
                           v_custocont_final
                          from pcest
                         where codprod = v_cprod
                           and codfilial = v_codfilial;
                        v_cmv_calculado := v_custofin_final;
                     exception
                        when no_data_found then
                           v_custofin_final := 0;
                           v_custoreal_final := 0;
                           v_custorep_final := 0;
                           v_custocont_final := 0;
                           v_cmv_calculado := 0;
                     end;
               end;
            else
               -- TRIBUTAÇÃO POR REGIÃO
               begin
                  select t.codst,
                         t.codicmtab,
                         t.codicmtabpf,
                         e.custofin,
                         e.custoreal,
                         e.custorep,
                         e.custocont,
                         ( ( ( v_vuncom * t.codicmtab ) / 100 ) + ( ( v_vuncom * v_txvenda ) / 100 ) + e.custofin ),
                         tbp.codstpartilha
                    into
                     v_codst_final,
                     v_codicmtab,
                     v_codicmtabpf,
                     v_custofin_final,
                     v_custoreal_final,
                     v_custorep_final,
                     v_custocont_final,
                     v_cmv_calculado,
                     v_codstpartilha
                    from pctributpartilha tbp,
                         pctribut t,
                         pcest e,
                         pctabpr a,
                         pcregiao d,
                         pcfilial f
                   where t.codst = tbp.codst
                     and a.codst = t.codst
                     and a.numregiao = d.numregiao
                     and a.codprod = e.codprod
                     and d.codfilial = e.codfilial
                     and f.numregiaopadrao = d.numregiao
                     and tbp.uf = v_uf_destinatario
                     and e.codprod = v_cprod
                     and e.codfilial = v_codfilial
                     and rownum = 1;
               exception
                  when no_data_found then
                     -- Se não encontrar por região, usar custos base da PCEST
                     begin
                        select nvl(
                           custofin,
                           0
                        ),
                               nvl(
                                  custoreal,
                                  0
                               ),
                               nvl(
                                  custorep,
                                  0
                               ),
                               nvl(
                                  custocont,
                                  0
                               )
                          into
                           v_custofin_final,
                           v_custoreal_final,
                           v_custorep_final,
                           v_custocont_final
                          from pcest
                         where codprod = v_cprod
                           and codfilial = v_codfilial;
                        v_cmv_calculado := v_custofin_final;
                     exception
                        when no_data_found then
                           v_custofin_final := 0;
                           v_custoreal_final := 0;
                           v_custorep_final := 0;
                           v_custocont_final := 0;
                           v_cmv_calculado := 0;
                     end;
               end;
            end if;

            -- Usar o CMV calculado como custo final
            v_custofin := v_cmv_calculado;
            v_custoreal := v_cmv_calculado;
            v_custorep := v_custorep_final;
            v_custocont := v_custocont_final;

            -- Calcular totais para acumulação
            v_total_custofin := v_custofin * v_qcom;
            v_total_custoreal := v_custoreal * v_qcom;
            v_total_custorep := v_custorep * v_qcom;
            v_total_custocont := v_custocont * v_qcom;
         end;
      end;
   end loop;

   -- Buscar dados adicionais necessários para PCNFSAID
   declare
      v_codfornec number;
   begin
      -- Buscar CODCONTCLI
      select codcontcli
        into v_codcontcli
        from pcconsum
       where rownum = 1;

      -- Buscar CODFORNEC baseado no tipo da nota
      if v_tpnf = '0' then
         -- Para notas de entrada: buscar baseado no CNPJ do emitente (fornecedor)
         begin
            select min(codfornec)
              into v_codfornec
              from pcfornec
             where regexp_replace(
               cgc,
               '[^0-9]',
               ''
            ) = regexp_replace(
               v_cgc_filial,
               '[^0-9]',
               ''
            );
         exception
            when no_data_found then
               v_codfornec := null;
         end;
      else
         -- Para notas de saída: buscar baseado no CNPJ da filial
         begin
            select min(codfornec)
              into v_codfornec
              from pcfornec
             where regexp_replace(
               cgc,
               '[^0-9]',
               ''
            ) = regexp_replace(
               v_cgc_filial,
               '[^0-9]',
               ''
            );
         exception
            when no_data_found then
               v_codfornec := null;
         end;
      end if;

      -- Inserir cabeçalho da nota baseado no tipo (PCNFSAID para saída, PCNFENT para entrada)
      if v_tpnf = '1' then
         -- NOTA DE SAÍDA: inserir em PCNFSAID
         dbms_output.put_line('📤 Inserindo nota de SAÍDA em PCNFSAID...');
         begin
            insert into pcnfsaid (
               numtransvenda,
               codfilial,
               codfilialnf,
               codcli,
               codclinf,
               chavenfe,
               serie,
               numnota,
               dtsaida,
               dtentrega,
               dtlancto,
               vltotal,
               vltotger,
               vlcustofin,
               vlcustoreal,
               vlcustorep,
               vlcustocont,
               tipoemissao,
               finalidadenfe,
               caixa,
               vlfrete,
               vloutrasdesp,
               vldesconto,
               tipovenda,
               icmsretido,
               bcst,
               vlipi,
               vlpis,
               vlcofins,
               codpais,
               descpais,
               cep,
               uf,
               municipio,
               endereco,
               numendereco,
               bairro,
               prazoponderado,
               perbaseredoutrasdesp,
               geracp,
               codfiscalfrete,
               percicmfrete,
               ambientenfe,
               conferido,
               agregastvlmerc,
               emissnumautomatico,
               cteregimeespecial,
               nfipiemitida,
               reduzicmsdocte,
               gerarbcrnfe,
               aliqicmoutrasdesp,
               codfiscaloutrasdesp,
               codsittribpiscofins,
               codfiscalnf,
               vlbaseipi,
               perpis,
               percofins,
               vlbasepiscofins,
               codcob,
               codplpag,
               condvenda,
               situacaonfe,
               especie,
               numcar,
               protocolonfe,
               codfiscal,
               codcont,
               codfornec,
               codusur,
               numvias,
               codpraca,
               numped,
               numpedcli,
               obs,
               obsnfe,
               dtfecha,
               despesasrateada
            ) values ( v_numtransvenda,
                       v_codfilial,
                       v_codfilial, -- codfilialnf
                       v_codcli,
                       v_codcli, -- codclinf
                       v_chavenfe,
                       v_serie,
                       v_numnota,
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtsaida
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtentrega
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtlancto
                       v_vltotal,
                       v_vltotal, -- vltotger
                       v_total_custofin,
                       v_total_custoreal,
                       v_total_custorep,
                       v_total_custocont,
                       v_tpimp, -- tipoemissao
                       v_finnfe, -- finalidadenfe
                       0, -- caixa
                       v_vfrete, -- vlfrete
                       v_voutros, -- vloutrasdesp
                       v_vdesc, -- vldesconto
                       '1', -- tipovenda
                       v_vst, -- icmsretido
                       v_baseicst, -- bcst
                       v_vipi, -- vlipi
                       v_vpis, -- vlpis
                       v_vcofins, -- vlcofins
                       v_cpais, -- codpais
                       v_xpais, -- descpais
                       v_cep, -- cep
                       v_uf_destinatario, -- uf
                       substr(
                          v_xmun,
                          1,
                          30
                       ), -- municipio
                       substr(
                          v_xlgr,
                          1,
                          40
                       ), -- endereco
                       substr(
                          v_nro,
                          1,
                          6
                       ), -- numendereco
                       substr(
                          v_xbairro,
                          1,
                          40
                       ), -- bairro
                       'N', -- prazoponderado
                       0, -- perbaseredoutrasdesp
                       'N', -- geracp
                       0, -- codfiscalfrete
                       0, -- percicmfrete
                       'P', -- ambientenfe
                       'N', -- conferido
                       'N', -- agregastvlmerc
                       'N', -- emissnumautomatico
                       'N', -- cteregimeespecial
                       'N', -- nfipiemitida
                       'N', -- reduzicmsdocte
                       'S', -- gerarbcrnfe
                       0, -- aliqicmoutrasdesp
                       0, -- codfiscaloutrasdesp
                       nvl(
                          v_cst_pis_cofins,
                          '00'
                       ), -- codsittribpiscofins
                       nvl(
                          v_cfop,
                          5102
                       ), -- codfiscalnf
                       v_vprod, -- vlbaseipi
                       0, -- perpis
                       0, -- percofins
                       0, -- vlbasepiscofins
                       nvl(
                          v_codcob,
                          'CML'
                       ), -- codcob
                       1, -- codplpag
                       1, -- condvenda
                       v_cstat, -- situacaonfe
                       'NF', -- especie
                       0, -- numcar
                       v_nprot, -- protocolonfe
                       case
                          when v_uf_destinatario = v_uf_emitente then
                             599
                          else
                             699
                       end, -- codfiscal
                       v_codcontcli, -- codcont
                       v_codfornec, -- codfornec
                       v_codrca, -- codusur
                       1, -- numvias
                       v_codpraca, -- codpraca
                       null, -- numped
                       v_xped, -- numpedcli
                       v_infadprod, -- obs
                       v_infcpl, -- obsnfe
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtfecha
                       'N' -- despesasrateada
                        );
         exception
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao inserir PCNFSAID: ' || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;

      else
         -- NOTA DE ENTRADA: inserir em PCNFENT
         dbms_output.put_line('📥 Inserindo nota de ENTRADA em PCNFENT...');
         declare
            v_codfornecnf number;
         begin
            -- Debug: verificar valores antes do INSERT
            dbms_output.put_line('🔍 DEBUG VALORES PCNFENT:');
            dbms_output.put_line('  - v_tpimp: '
                                 || nvl(
               to_char(v_tpimp),
               'NULL'
            ));
            dbms_output.put_line('  - v_finnfe: '
                                 || nvl(
               to_char(v_finnfe),
               'NULL'
            ));
            dbms_output.put_line('  - v_serie: ' || nvl(
               v_serie,
               'NULL'
            ));
            dbms_output.put_line('  - v_numtransent: '
                                 || nvl(
               to_char(v_numtransent),
               'NULL'
            ));
            dbms_output.put_line('  - v_numnota: '
                                 || nvl(
               to_char(v_numnota),
               'NULL'
            ));
            dbms_output.put_line('  - v_cstat: '
                                 || nvl(
               to_char(v_cstat),
               'NULL'
            ));
            dbms_output.put_line('  - v_codfilial: '
                                 || nvl(
               to_char(v_codfilial),
               'NULL'
            ));
            dbms_output.put_line('  - v_codcontfor: '
                                 || nvl(
               to_char(v_codcontfor),
               'NULL'
            ));
            dbms_output.put_line('  - v_cgcent: ' || nvl(
               v_cgcent,
               'NULL'
            ));
            dbms_output.put_line('  - v_coddevol: '
                                 || nvl(
               to_char(v_coddevol),
               'NULL'
            ));
            dbms_output.put_line('  - v_codrca: '
                                 || nvl(
               to_char(v_codrca),
               'NULL'
            ));
            dbms_output.put_line('  - v_cpais: ' || nvl(
               v_cpais,
               'NULL'
            ));
            dbms_output.put_line('  - v_xpais: ' || nvl(
               v_xpais,
               'NULL'
            ));
            dbms_output.put_line('  - v_modelo: ' || nvl(
               v_modelo,
               'NULL'
            ));
            dbms_output.put_line('  - v_nprot: ' || nvl(
               v_nprot,
               'NULL'
            ));

            -- Debug valores numéricos críticos
            dbms_output.put_line('🔍 DEBUG VALORES NUMÉRICOS:');
            dbms_output.put_line('  - v_vfrete: '
                                 || nvl(
               to_char(v_vfrete),
               'NULL'
            ));
            dbms_output.put_line('  - v_vdesc: '
                                 || nvl(
               to_char(v_vdesc),
               'NULL'
            ));
            dbms_output.put_line('  - v_vltotal: '
                                 || nvl(
               to_char(v_vltotal),
               'NULL'
            ));
            dbms_output.put_line('  - v_vst: '
                                 || nvl(
               to_char(v_vst),
               'NULL'
            ));
            dbms_output.put_line('  - v_baseicst: '
                                 || nvl(
               to_char(v_baseicst),
               'NULL'
            ));
            dbms_output.put_line('  - v_vipidevol: '
                                 || nvl(
               to_char(v_vipidevol),
               'NULL'
            ));
            dbms_output.put_line('  - v_vipi: '
                                 || nvl(
               to_char(v_vipi),
               'NULL'
            ));
            dbms_output.put_line('  - v_vpis: '
                                 || nvl(
               to_char(v_vpis),
               'NULL'
            ));
            dbms_output.put_line('  - v_vcofins: '
                                 || nvl(
               to_char(v_vcofins),
               'NULL'
            ));
            dbms_output.put_line('  - v_vprod: '
                                 || nvl(
               to_char(v_vprod),
               'NULL'
            ));

            -- Para devolução: buscar na PCCLIENT, para outras entradas: buscar na PCFORNEC
            if v_is_devolucao = 'S' then
               -- Devolução: buscar CODFORNECNF na PCCLIENT usando CNPJ do destinatário
               begin
                  select min(codcli)
                    into v_codfornecnf
                    from pcclient
                   where regexp_replace(
                     cgcent,
                     '[^0-9]',
                     ''
                  ) = regexp_replace(
                     v_cgcent,
                     '[^0-9]',
                     ''
                  );

                  dbms_output.put_line('🔍 Devolução - Cliente encontrado: ' || nvl(
                     v_codfornecnf,
                     'NULL'
                  ));
               exception
                  when no_data_found then
                     v_codfornecnf := null;
                     dbms_output.put_line('⚠️ Devolução - Cliente não encontrado para CNPJ: ' || v_cgcent);
               end;
            else
               -- Outras entradas: buscar CODFORNEC na PCFORNEC usando CNPJ do emitente
               begin
                  select min(codfornec)
                    into v_codfornecnf
                    from pcfornec
                   where regexp_replace(
                     cgc,
                     '[^0-9]',
                     ''
                  ) = regexp_replace(
                     v_cgc_filial,
                     '[^0-9]',
                     ''
                  );

                  dbms_output.put_line('🔍 Entrada - Fornecedor encontrado: ' || nvl(
                     v_codfornecnf,
                     'NULL'
                  ));
               exception
                  when no_data_found then
                     v_codfornecnf := null;
                     dbms_output.put_line('⚠️ Entrada - Fornecedor não encontrado para CNPJ: ' || v_cgc_filial);
               end;
            end if;

            -- Validar se v_codfornecnf foi encontrado
            if v_codfornecnf is null then
               dbms_output.put_line('⚠️ AVISO: v_codfornecnf é NULL - usando valor padrão 1');
               v_codfornecnf := 1; -- Valor padrão
            end if;

            -- Tentar INSERT campo por campo para identificar o problema
            dbms_output.put_line('🔍 Tentando INSERT PCNFENT...');

            -- Primeiro, vamos testar os valores que podem estar problemáticos
            begin
               dbms_output.put_line('Testando conversões:');
               dbms_output.put_line('  - to_date(v_dtemissao): '
                                    || to_char(
                  to_date(substr(
                     v_dtemissao,
                     1,
                     10
                  ),
        'DD/MM/YYYY'),
                  'DD/MM/YYYY'
               ));
               dbms_output.put_line('  - v_tpimp: ' || v_tpimp);
               dbms_output.put_line('  - v_finnfe: ' || v_finnfe);
               dbms_output.put_line('  - v_serie: ' || v_serie);
               dbms_output.put_line('  - v_numtransent: ' || v_numtransent);
               dbms_output.put_line('  - v_numnota: ' || v_numnota);
               dbms_output.put_line('  - v_codfornecnf: '
                                    || nvl(
                  to_char(v_codfornecnf),
                  'NULL'
               ));
            exception
               when others then
                  dbms_output.put_line('ERRO na validação inicial: ' || sqlerrm);
                  raise;
            end;

            insert into pcnfent (
               dtemissao,
               tipoemissao,
               finalidadenfe,
               serie,
               numtransent,
               numnota,
               vlfrete,
               vldesconto,
               dtent,
               vltotal,
               vlst,
               baseicst,
               vlipi,
               vlpis,
               vlcofins,
               codpais,
               descpais,
               cep,
               uf,
               municipio,
               endereco,
               bairro,
               vltotger,
               chavenfe,
               perbaseredoutrasdesp,
               codfiscalfrete,
               percicmfrete,
               ambientenfe,
               conferido,
               agregastvlmerc,
               gerarbcrnfe,
               dtlancto,
               vlbaseipi,
               situacaonfe,
               especie,
               codfilial,
               codfilialnf,
               protocolonfe,
               codfiscal,
               codcont,
               tipodescarga,
               cgc,
               codfornecnf,
               codfornec,
               geranfdevcli,
               geranfvenda,
               modelo,
               coddevol,
               emissaopropria,
               codusurdevol,
               numvias,
               aliqicmoutrasdesp,
               codfiscaloutrasdesp,
               perpis,
               percofins,
               obs,
               obsnfe,
               dthoraautorizacaosefaz
            ) values ( to_date(substr(
               v_dtemissao,
               1,
               10
            ),
        'DD/MM/YYYY'), -- dtemissao
                       v_tpimp, -- tipoemissao
                       v_finnfe, -- finalidadenfe
                       v_serie, -- serie
                       v_numtransent, -- numtransent
                       v_numnota, -- numnota
                       v_vfrete, -- vlfrete
                       v_vdesc, -- vldesconto
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtent
                       v_vltotal, -- vltotal
                       v_vst, -- vlst
                       v_baseicst, -- baseicst
                       case
                          when v_is_devolucao = 'S' then
                             v_vipidevol
                          else
                             v_vipi
                       end, -- vlipi
                       v_vpis, -- vlpis
                       v_vcofins, -- vlcofins
                       to_number(v_cpais), -- codpais
                       v_xpais, -- descpais
                       v_cep, -- cep
                       v_uf_destinatario, -- uf
                       substr(
                          v_xmun,
                          1,
                          30
                       ), -- municipio
                       substr(
                          v_xlgr,
                          1,
                          40
                       ), -- endereco
                       substr(
                          v_xbairro,
                          1,
                          40
                       ), -- bairro
                       v_vltotal, -- vltotger
                       v_chavenfe, -- chavenfe
                       0, -- perbaseredoutrasdesp
                       0, -- codfiscalfrete
                       0, -- percicmfrete
                       'P', -- ambientenfe
                       'N', -- conferido
                       'N', -- agregastvlmerc
                       'S', -- gerarbcrnfe
                       to_date(substr(
                          v_dtemissao,
                          1,
                          10
                       ),
                               'DD/MM/YYYY'), -- dtlancto
                       v_vprod, -- vlbaseipi
                       v_cstat, -- situacaonfe
                       'NF', -- especie
                       v_codfilial, -- codfilial
                       v_codfilial, -- codfilialnf
                       v_nprot, -- protocolonfe
                       case
                          when v_is_devolucao = 'S' then
                                case
                                   when v_uf_destinatario = v_uf_emitente then
                                      132
                                   else
                                      232
                                end
                          else
                             case
                                when v_uf_destinatario = v_uf_emitente then
                                      112
                                else
                                   212
                             end
                       end, -- codfiscal
                       v_codcontfor, -- codcont
                       case
                          when v_is_devolucao = 'S' then
                             '6'
                          else
                             'R'
                       end, -- tipodescarga
                       v_cgcent, -- cgc
                       v_codfornecnf, -- codfornecnf
                       v_codfornecnf, -- codfornec
                       case
                          when v_is_devolucao = 'S' then
                             'S'
                          else
                             null
                       end, -- geranfdevcli
                       case
                          when v_is_devolucao = 'N' then
                             'S'
                          else
                             null
                       end, -- geranfvenda
                       v_modelo, -- modelo
                       case
                          when v_is_devolucao = 'S' then
                             v_coddevol
                          else
                             null
                       end, -- coddevol
                       'N', -- emissaopropria
                       case
                          when v_is_devolucao = 'S' then
                             v_codrca
                          else
                             null
                       end, -- codusurdevol
                       1, -- numvias
                       0, -- aliqicmoutrasdesp
                       0, -- codfiscaloutrasdesp
                       0, -- perpis
                       0, -- percofins
                       v_infadprod, -- obs
                       v_infcpl, -- obsnfe
                       case
                          when v_is_devolucao = 'S' then
                             to_date(v_dtemissao,
                                     'DD/MM/YYYY HH24:MI:SS')
                          else
                             null
                       end -- dthoraautorizacaosefaz
            );
         exception
            when others then
               rollback to inicio_processamento;
               p_retorno := 'ERROR: Falha ao inserir PCNFENT: ' || sqlerrm;
               raise_application_error(
                  -20001,
                  p_retorno
               );
         end;
      end if;
   exception
      when others then
         rollback to inicio_processamento;
         p_retorno := 'ERROR: Falha no processamento do cabeçalho: ' || sqlerrm;
         raise_application_error(
            -20001,
            p_retorno
         );
   end;

   -- Inserir registro base da nota (PCNFBASE)
   declare
      v_base_total number;
      v_icms_total number;
      v_aliq       number;
      v_cfop_base  number;
   begin
      -- Usar valores totais do cabeçalho
      v_base_total := v_vbc;
      v_icms_total := v_vicms;
      v_cfop_base := nvl(
         v_cfop,
         5102
      ); -- CFOP do XML ou padrão para venda

      if
         v_base_total > 0
         and v_icms_total > 0
      then
         v_aliq := round(
            (v_icms_total / v_base_total) * 100,
            2
         );
      else
         v_aliq := 0;
      end if;

      begin
         insert into pcnfbase (
            numtransvenda,
            numtransent,
            codcont,
            codfiscal,
            vlbase,
            vlicms,
            aliquota
         ) values (
            case
               when v_tpnf = '1' then
                  v_numtransvenda
               else
                  null
            end, -- numtransvenda (saída)
            case
               when v_tpnf = '0' then
                  v_numtransent
               else
                  null
            end, -- numtransent (entrada)
            v_codcontcli,
            v_cfop_base,
            v_base_total,
            v_icms_total,
            v_aliq );
      exception
         when others then
            rollback to inicio_processamento;
            p_retorno := 'ERROR: Falha ao inserir PCNFBASE: ' || sqlerrm;
            raise_application_error(
               -20001,
               p_retorno
            );
      end;
   end;

   -- Inserir prestação (PCPREST)
   declare
      v_codcob_prest varchar2(10);
   begin
      v_codcob_prest := nvl(
         v_codcob,
         'CML'
      );
      begin
         insert into pcprest (
            codcli,
            codfilial,
            codfilialnf,
            numtransvenda,
            valor,
            prest,
            duplic,
            dtvenc,
            dtvencorig,
            dtemissao,
            codcob,
            codcoborig,
            status,
            valororig,
            txperm,
            operacao,
            codusur,
            numcar,
            codfunccxmot,
            dttransacaocc,
            dtcxmot,
            dtcxmothhmmss,
            dtultalter,
            codfuncultalter,
            tipooperacaotef,
            numtrans
         ) values ( v_codcli,
                    v_codfilial,
                    v_codfilial, -- codfilialnf
                    v_numtransvenda,
                    v_vltotal, -- valor
                    1, -- prest
                    v_vltotal, -- duplic
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY') + 45, -- dtvenc (45 dias)
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtvencorig
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtemissao
                    v_codcob_prest, -- codcob
                    v_codcob_prest, -- codcoborig
                    'A', -- status
                    v_vltotal, -- valororig
                    0, -- txperm
                    'S', -- operacao
                    v_codrca, -- codusur
                    0, -- numcar
                    v_codfunc, -- codfunccxmot
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dttransacaocc
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtcxmot
                    to_date(v_dtemissao,
                            'DD/MM/YYYY HH24:MI:SS'), -- dtcxmothhmmss
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtultalter
                    v_codfunc, -- codfuncultalter
                    'C', -- tipooperacaotef
                    v_proxnumtrans -- numtrans
                     );
      exception
         when others then
            rollback to inicio_processamento;
            p_retorno := 'ERROR: Falha ao inserir PCPREST: ' || sqlerrm;
            raise_application_error(
               -20001,
               p_retorno
            );
      end;
   end;

   -- Inserir observações da prestação (PCPRESTOBS)
   declare
      v_obsgerais varchar2(200);
   begin
      v_obsgerais := nvl(
         v_infcpl,
         'Importado via XML - ' || v_chavenfe
      );
      insert into pcprestobs (
         numtransvenda,
         prest,
         pendencia,
         obsgerais
      ) values ( v_numtransvenda,
                 1, -- primeira prestação
                 'N', -- sem pendência
                 v_obsgerais );
   end;

   -- Processamento específico para notas de entrada (devoluções)
   if
      v_tpnf = '0'
      and v_is_devolucao = 'S'
      and v_refnfe is not null
   then

      -- Inserir PCESTCOM apenas para devoluções
      declare
         v_vlestornocmv number;
      begin
            -- Buscar dados da nota original
         begin
            select numtransvenda,
                   vlcustofin,
                   numnota
              into
               v_numtransvenda_ref,
               v_vlestornocmv,
               v_numnota_ref
              from pcnfsaid
             where chavenfe = v_refnfe
               and dtcancel is null;
         exception
            when no_data_found then
               v_numtransvenda_ref := null;
               v_vlestornocmv := 0;
               v_numnota_ref := null;
         end;

            -- Inserir na PCESTCOM
         insert into pcestcom (
            numtransent,
            vldevolucao,
            vlestorno,
            dtestorno,
            codusur,
            codfunc,
            numtransvenda,
            historico,
            vlestornocmv,
            codusur2,
            codusur3,
            codusur4,
            vlestorno2,
            vlestorno3,
            vlestorno4
         ) values ( v_numtransent, -- numtransent
                    v_vltotal, -- vldevolucao
                    0, -- vlestorno
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtestorno
                    v_codrca, -- codusur
                    v_codfunc, -- codfunc
                    v_numtransvenda_ref, -- numtransvenda (da nota original)
                    'MERCADO LIVRE', -- historico
                    nvl(
                       v_vlestornocmv,
                       0
                    ), -- vlestornocmv
                    0, -- codusur2
                    0, -- codusur3
                    0, -- codusur4
                    0, -- vlestorno2
                    0, -- vlestorno3
                    0  -- vlestorno4
                     );

            -- Inserir na PCCRECLI (crédito de devolução)
         insert into pccrecli (
            codcli,
            dtlanc,
            codfilial,
            valor,
            numnota,
            numtrans,
            codfunc,
            historico,
            codfunclanc,
            numerario,
            codmovimento,
            codrotina,
            numcred
         ) values ( v_codcli, -- codcli
                    to_date(substr(
                       v_dtemissao,
                       1,
                       10
                    ),
                            'DD/MM/YYYY'), -- dtlanc
                    v_codfilial, -- codfilial
                    v_vltotal, -- valor
                    v_numnota, -- numnota (da devolução)
                    v_numtransent, -- numtrans
                    v_codfunc, -- codfunc
                    'CREDITO DEVOLUCAO - REF. NF: '
                    || nvl(
                       to_char(v_numnota_ref),
                       'N/A'
                    ), -- historico
                    v_codfunc, -- codfunclanc
                    'N', -- numerario
                    '250004', -- codmovimento
                    '618', -- codrotina
                    v_proxnumcred -- numcred
                     );

            -- Fazer baixa do título da nota original usando crédito
         if v_numtransvenda_ref is not null then
            update pcprest
               set vpago = v_vltotal,
                   dtpag = to_date(substr(
                      v_dtemissao,
                      1,
                      10
                   ),
            'DD/MM/YYYY'),
                   codbaixa = 1,
                   dtbaixa = to_date(substr(
                      v_dtemissao,
                      1,
                      10
                   ),
            'DD/MM/YYYY'),
                   codcoborig = 'C',
                   dtbaixacred = to_date(substr(
                      v_dtemissao,
                      1,
                      10
                   ),
            'DD/MM/YYYY'),
                   dtultalter = to_date(substr(
                      v_dtemissao,
                      1,
                      10
                   ),
            'DD/MM/YYYY'),
                   txperm = 0,
                   valormulta = 0,
                   codcob = 'CRED',
                   codfuncultalter = v_codfunc
             where numtransvenda = v_numtransvenda_ref
               and prest = 1; -- Assumindo primeira prestação

               -- Baixar o crédito do cliente (usar o crédito)
            update pccrecli
               set dtdesconto = to_date(substr(
               v_dtemissao,
               1,
               10
            ),
        'DD/MM/YYYY'),
                   numnotadesc = v_numnota_ref,
                   numtransvenda = v_numtransvenda_ref
             where numtrans = v_numtransent;
         end if;
      exception
         when others then
            rollback to inicio_processamento;
            p_retorno := 'ERROR: Falha no processamento de devolução: ' || sqlerrm;
            raise_application_error(
               -20001,
               p_retorno
            );
      end;
   end if;

   -- Atualizar custos na PCMOV e PCNFSAID (apenas para notas de saída)
   if v_tpnf = '1' then
      declare
         v_custofin_total  number;
         v_custoreal_total number;
         v_custorep_total  number;
         v_custocont_total number;
      begin
         select round(
            sum(custofin * qt),
            2
         ),
                round(
                   sum(custoreal * qt),
                   2
                ),
                round(
                   sum(custorep * qt),
                   2
                ),
                round(
                   sum(custocont * qt),
                   2
                )
           into
            v_custofin_total,
            v_custoreal_total,
            v_custorep_total,
            v_custocont_total
           from pcmov
          where numtransvenda = v_numtransvenda;

         update pcnfsaid
            set vlcustofin = v_custofin_total,
                vlcustoreal = v_custoreal_total,
                vlcustorep = v_custorep_total,
                vlcustocont = v_custocont_total
          where numtransvenda = v_numtransvenda;
      exception
         when others then
            rollback to inicio_processamento;
            p_retorno := 'ERROR: Falha ao atualizar custos: ' || sqlerrm;
            raise_application_error(
               -20001,
               p_retorno
            );
      end;
   end if;

   -- =====================================================
   -- ATUALIZAR SEQUÊNCIAS E FINALIZAR TRANSAÇÃO
   -- =====================================================
   begin
      -- Atualizar as sequências apenas no final, após todos os inserts
      if v_tpnf = '1' then
         -- Para notas de saída: atualizar PROXNUMTRANSVENDA
         update pcconsum
            set
            proxnumtransvenda = proxnumtransvenda + 1
          where rownum = 1;
      else
         -- Para notas de entrada: atualizar PROXNUMTRANSENT
         update pcconsum
            set
            proxnumtransent = proxnumtransent + 1
          where rownum = 1;
      end if;

      -- Confirmar todas as transações
      commit;
      p_retorno := 'SUCCESS: NFe processada com sucesso - Chave: ' || v_chavenfe;
      dbms_output.put_line('✅ Processamento concluído com sucesso!');
      dbms_output.put_line('📋 Chave NFe: ' || v_chavenfe);
      dbms_output.put_line('🔢 Tipo: '
                           || case
         when v_tpnf = '1' then
            'Saída'
         else 'Entrada'
      end);
      dbms_output.put_line('🆔 Transação: '
                           || case
         when v_tpnf = '1' then
            v_numtransvenda
         else v_numtransent
      end);

   exception
      when others then
         rollback to inicio_processamento;
         p_retorno := 'ERROR: Falha na finalização: ' || sqlerrm;
         dbms_output.put_line('❌ Erro na finalização: ' || sqlerrm);
         raise_application_error(
            -20001,
            p_retorno
         );
   end;

exception
   when others then
      -- Rollback completo em caso de qualquer erro
      rollback;
      p_retorno := 'ERROR: ' || sqlerrm;
      dbms_output.put_line('💥 ERRO GERAL no processamento: ' || sqlerrm);
      dbms_output.put_line('🔄 Todas as operações foram desfeitas (rollback)');
      raise_application_error(
         -20001,
         p_retorno
      );
end nervsflow_integradora;
/