const xmlData = $json;

// Função para limpar texto e torná-lo seguro para JSON
function cleanText(text) {
  if (!text) return '';
  return text
    .replace(/[\r\n\t\f\v]/g, ' ')  // Remove quebras de linha e tabs
    .replace(/\s+/g, ' ')           // Múltiplos espaços viram um só
    .replace(/"/g, "'")             // Aspas duplas viram simples
    .replace(/\\/g, '/')            // Barras invertidas viram normais
    .trim();                        // Remove espaços do início/fim
}

try {
  // Validação básica
  if (!xmlData?.nfeProc?.NFe?.infNFe) {
    throw new Error('Estrutura XML inválida');
  }
  
  const nfe = xmlData.nfeProc.NFe.infNFe;
  const ide = nfe.ide;
  const emit = nfe.emit;
  const dest = nfe.dest;
  const total = nfe.total.ICMSTot;
  const protNFe = xmlData.nfeProc.protNFe;
  
  // Extrair chave NFe de forma segura
  let chavenfe = '';
  if (nfe.$ && nfe.$.Id) {
    chavenfe = nfe.$.Id.replace('NFe', '');
  } else if (protNFe?.infProt?.chNFe) {
    chavenfe = protNFe.infProt.chNFe;
  }
  
  // Extrair chave de referência (para devoluções)
  let refnfe = '';
  if (ide.NFref && ide.NFref.refNFe) {
    if (Array.isArray(ide.NFref.refNFe)) {
      refnfe = ide.NFref.refNFe[0] || '';
    } else {
      refnfe = ide.NFref.refNFe || '';
    }
  } else if (ide.NFref && Array.isArray(ide.NFref)) {
    refnfe = ide.NFref[0]?.refNFe || '';
  }

  // DADOS DO CABEÇALHO (serão repetidos para cada item)
  const dtemissao = ide.dhEmi ? (() => {
    const date = new Date(ide.dhEmi);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  })() : '';

  const tipofj = dest?.CNPJ ? 'J' : dest?.CPF ? 'F' : 'J';
  const codcob = ide.verProc?.includes('mercadolivre') ? 'CML' :
                 ide.verProc?.includes('Bling') ? 'D2C' : 'CML';
  
  // PROCESSAR ITENS
  let detList = nfe.det;
  if (!Array.isArray(detList)) {
    detList = [detList];
  }
  
  const devolucaoCFOPs = [
    '1201','1202','1203','1204','1208','1209','1212','1213','1214','1215','1216',
    '1410','1411','1503','1504','1505','1506','1553','1660','1661','1662','1918','1919',
    '2201','2202','2203','2204','2208','2209','2212','2213','2214','2215','2216',
    '2410','2411','2503','2504','2505','2506','2553','2660','2661','2662','2918','2919',
    '3201','3202','3211','3212','3503','3553',
    '5201','5202','5208','5209','5210','5213','5214','5215','5216','5410','5411','5412','5413',
    '5503','5553','5555','5556','5660','5661','5662','5918','5919','5921',
    '6201','6202','6208','6209','6210','6213','6214','6215','6216','6410','6411','6412','6413',
    '6503','6553','6555','6556','6660','6661','6662','6918','6919','6921',
    '7201','7202','7210','7211','7212','7553','7556','7930'
  ];
  
  // ARRAY FINAL PARA ORACLE (cabeçalho + cada item)
  const arrayParaOracle = [];
  
  for (const det of detList) {
    if (det.prod) {
      const nItem = det.$ ? parseInt(det.$.nItem) : parseInt(det.nItem || 1);
      const produto = det.prod;
      const imposto = det.imposto;
      
      // Encontrar tipo de ICMS
      const icms = imposto?.ICMS;
      const icmsObj = [
        'ICMS00', 'ICMS10', 'ICMS20', 'ICMS30', 'ICMS40', 'ICMS50', 'ICMS60', 'ICMS70', 'ICMS80',
        'ICMSSN101', 'ICMSSN102', 'ICMSSN103', 'ICMSSN201', 'ICMSSN202', 'ICMSSN203', 
        'ICMSSN300', 'ICMSSN400', 'ICMSSN500', 'ICMSSN900'
      ].find(obj => icms && icms[obj]);
      
      const icmsData = icms?.[icmsObj] || {};
      
      // PIS/COFINS
      const pis = imposto?.PIS?.PISAliq || imposto?.PIS?.PISOutr || imposto?.PIS;
      const cofins = imposto?.COFINS?.COFINSAliq || imposto?.COFINS?.COFINSOutr || imposto?.COFINS;
      const CST_PIS_COFINS = pis?.CST || cofins?.CST || '00';
      
      // IPI
      const ipi = imposto?.IPI?.IPITrib || imposto?.IPI;
      
      // Detecção de devolução
      const isDevolucao = devolucaoCFOPs.includes(produto.CFOP);
      
      // Cálculos
      const qcom = parseFloat(produto.qCom);
      const vuncom = parseFloat(produto.vUnCom);
      const vprod = parseFloat(produto.vProd);
      const vdesc = parseFloat(produto.vDesc || 0);
      const vfrete = parseFloat(produto.vFrete || 0);
      const vipi = parseFloat(ipi?.vIPI || 0);
      const vipidev = parseFloat(ipi?.vIPIDevol || 0);
      const vicms = parseFloat(icmsData.vICMS || 0);
      const vbc = parseFloat(icmsData.vBC || 0);
      
      const vIPIItem = vipi && qcom ? vipi / qcom : 0;
      const vIPIDevol = isDevolucao && vipidev ? vipidev / qcom : 0;
      const vDescItem = vdesc ? vdesc / qcom : 0;
      const vFreteItem = vfrete ? vfrete / qcom : 0;
      
      // PUNITCONT com lógica de devolução
      // Para devoluções: usar vIPIItem quando vIPIDevol estiver zerado
      const PUNITCONT = parseFloat(
        (vuncom + (isDevolucao ? (vIPIDevol > 0 ? vIPIDevol : vIPIItem) : vIPIItem) - vDescItem).toFixed(3)
      );
      
      // REGISTRO COMPLETO (CABEÇALHO + ITEM) PARA ORACLE
      const registro = {
        // DADOS DO CABEÇALHO (repetidos para cada item)
        chavenfe: chavenfe,
        serie: ide.serie,
        numnota: parseInt(ide.nNF),
        tpnf: parseInt(ide.tpNF),
        finnfe: parseInt(ide.finNFe),
        tpimp: parseInt(ide.tpImp),
        modelo: ide.mod,
        natop: cleanText(ide.natOp),
        dtemissao: dtemissao,
        cstat: protNFe?.infProt?.cStat,
        nprot: protNFe?.infProt?.nProt || '',
        cnpjemitente: emit.CNPJ || emit.CPF,
        ufemitente: emit.enderEmit.UF,
        cnpjdestinatario: dest?.CNPJ || dest?.CPF,
        ufdestinatario: dest?.enderDest?.UF,
        refnfe: refnfe,
        tipofj: tipofj,
        codcob: codcob,
        infcpl: cleanText(nfe.infAdic?.infCpl),
        is_devolucao: isDevolucao ? 'S' : 'N',

        // DADOS DE ENDEREÇO (repetidos para cada item)
        xlgr: dest?.enderDest?.xLgr,
        nro: dest?.enderDest?.nro,
        xbairro: dest?.enderDest?.xBairro,
        xmun: dest?.enderDest?.xMun,
        xpais: dest?.enderDest?.xPais,
        cpais: dest?.enderDest?.cPais,
        cmun: dest?.enderDest?.cMun,
        cep: dest?.enderDest?.CEP,
        indfinal: ide.indFinal,
        indiedest: dest?.indIEDest,

        // TOTAIS DA NFe (repetidos para cada item)
        vltotal: parseFloat(total.vNF),
        vprod: parseFloat(total.vProd),
        vicms: parseFloat(total.vICMS || 0),
        vbc: parseFloat(total.vBC || 0),
        vpis: parseFloat(total.vPIS || 0),
        vcofins: parseFloat(total.vCOFINS || 0),
        vipi: parseFloat(total.vIPI || 0),
        vfrete: parseFloat(total.vFrete || 0),
        voutros: parseFloat(total.vOutro || 0),
        vdesc: parseFloat(total.vDesc || 0),
        vst: parseFloat(total.vST || 0),
        baseicst: parseFloat(total.vBCST || 0),
        vipidevol: vipidev, // Valor total do IPI de devolução
        
        // DADOS DO ITEM (específicos de cada item)
        nitem: nItem,
        cprod: produto.cProd,
        cean: produto.cEAN || '',
        xprod: cleanText(produto.xProd),
        ncm: produto.NCM,
        cfop: parseInt(produto.CFOP),
        ucom: produto.uCom,
        qcom: qcom,
        vuncom: vuncom,
        vprod_item: vprod,
        vdesc_item: vdesc,
        vfrete_item: vfrete,
        voutro_item: parseFloat(produto.vOutro || 0),
        vfreiteitem: vFreteItem,
        punitcont: PUNITCONT,
        
        // ICMS do item
        orig: icmsData.orig,
        cst: icmsData.CST,
        picms: parseFloat(icmsData.pICMS || 0),
        vbc_item: vbc,
        vicms_item: vicms,
        pst: parseFloat(icmsData.pST || 0),
        
        // PIS/COFINS do item
        ppis: parseFloat(pis?.pPIS || 0),
        vbcpis: parseFloat(pis?.vBC || 0),
        vpis_item: parseFloat(pis?.vPIS || 0),
        pcofins: parseFloat(cofins?.pCOFINS || 0),
        vbccofins: parseFloat(cofins?.vBC || 0),
        vcofins_item: parseFloat(cofins?.vCOFINS || 0),
        cst_pis_cofins: CST_PIS_COFINS,
        
        // IPI do item
        pipi: parseFloat(ipi?.pIPI || 0),
        vbcipi: parseFloat(ipi?.vBC || 0),
        vipi_item: vipi,
        
        // DIFAL do item
        vbcufdest: parseFloat(imposto?.ICMSUFDest?.vBCUFDest || 0),
        picmsufdest: parseFloat(imposto?.ICMSUFDest?.pICMSUFDest || 0),
        picmsinter: parseFloat(imposto?.ICMSUFDest?.pICMSInter || 0),
        picmsinterpart: parseFloat(imposto?.ICMSUFDest?.pICMSInterPart || 0),
        vlicmspartdest: parseFloat(imposto?.ICMSUFDest?.vICMSUFDest || 0),
        vlicmspartrem: parseFloat(imposto?.ICMSUFDest?.vICMSUFRemet || 0),
        pfcpufdest: parseFloat(imposto?.ICMSUFDest?.pFCPUFDest || 0),
        vlfcppart: parseFloat(imposto?.ICMSUFDest?.vFCPUFDest || 0),
        
        // Campos adicionais do item
        codcest: produto.CEST || '',
        infadprod: cleanText(det.infAdProd)
      };
      
      arrayParaOracle.push(registro);
    }
  }
  
  // RETORNAR UMA ÚNICA LINHA COM ARRAY DE ITENS
  return {
    itens: arrayParaOracle
  };

} catch (error) {
  return {
    erro: true,
    mensagem: error.message,
    stack: error.stack
  };
}
