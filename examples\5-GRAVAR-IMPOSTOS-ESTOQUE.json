{"nodes": [{"parameters": {}, "id": "60d36f8e-8a88-45eb-8f23-02f99ad6ecba", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-5200, 8140]}, {"parameters": {"setAllData": false, "options": {}}, "id": "dd3f282a-3df7-4618-8478-03a7a3884511", "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [-4900, 8140]}, {"parameters": {"options": {}}, "id": "5b06c687-8a60-4038-ac76-af1ceb4fe8ce", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-4760, 8140]}, {"parameters": {"fileSelector": "/u01/NERVSFLOW/*.xml"}, "id": "d5091918-34ea-4a44-9268-64b2f913d039", "name": "Ler XML no P:/", "type": "n8n-nodes-base.readBinaryFiles", "typeVersion": 1, "position": [-5040, 8140]}, {"parameters": {"jsCode": "// 5-Impostos e Estoque\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'])\n    const vFreteTotal = parseFloat(ICMSTot['vFrete'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0)\n    // Obter a natureza da operação já utilizada para resultItem\n    const natOp = xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp']\n    // Lista de CFOPs de devolução\n    const devolucaoCFOPs = [\n      '1201',\n      '1202',\n      '1203',\n      '1204',\n      '1208',\n      '1209',\n      '1212',\n      '1213',\n      '1214',\n      '1215',\n      '1216',\n      '1410',\n      '1411',\n      '1503',\n      '1504',\n      '1505',\n      '1506',\n      '1553',\n      '1660',\n      '1661',\n      '1662',\n      '1918',\n      '1919',\n      '2201',\n      '2202',\n      '2203',\n      '2204',\n      '2208',\n      '2209',\n      '2212',\n      '2213',\n      '2214',\n      '2215',\n      '2216',\n      '2410',\n      '2411',\n      '2503',\n      '2504',\n      '2505',\n      '2506',\n      '2553',\n      '2660',\n      '2661',\n      '2662',\n      '2918',\n      '2919',\n      '3201',\n      '3202',\n      '3211',\n      '3212',\n      '3503',\n      '3553',\n      '5201',\n      '5202',\n      '5208',\n      '5209',\n      '5210',\n      '5213',\n      '5214',\n      '5215',\n      '5216',\n      '5410',\n      '5411',\n      '5412',\n      '5413',\n      '5503',\n      '5553',\n      '5555',\n      '5556',\n      '5660',\n      '5661',\n      '5662',\n      '5918',\n      '5919',\n      '5921',\n      '6201',\n      '6202',\n      '6208',\n      '6209',\n      '6210',\n      '6213',\n      '6214',\n      '6215',\n      '6216',\n      '6410',\n      '6411',\n      '6412',\n      '6413',\n      '6503',\n      '6553',\n      '6555',\n      '6556',\n      '6660',\n      '6661',\n      '6662',\n      '6918',\n      '6919',\n      '6921',\n      '7201',\n      '7202',\n      '7210',\n      '7211',\n      '7212',\n      '7553',\n      '7556',\n      '7930'\n    ]\n\n    for (const det of detList) {\n      if (det['prod']) {\n        const nItem = det['nItem']\n        const produto = det['prod']\n        const imposto = det['imposto']\n\n        // Simplificando a captura do vIPIDevol\n        const vIPIDevol =\n          det['impostoDevol'] &&\n          det['impostoDevol']['IPI'] &&\n          det['impostoDevol']['IPI']['vIPIDevol']\n            ? parseFloat(det['impostoDevol']['IPI']['vIPIDevol']) /\n              parseFloat(produto['qCom'])\n            : 0\n\n        // Correção no acesso às variáveis PIS e COFINS\n        const pis =\n          imposto &&\n          imposto['PIS'] &&\n          (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr'])\n        const cofins =\n          imposto &&\n          imposto['COFINS'] &&\n          (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr'])\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const vBCUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vBCUFDest']) ||\n          0\n        const pICMSUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSUFDest']) ||\n          0\n        const pICMSInter =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInter']) ||\n          0\n        const pICMSInterPart =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pICMSInterPart']) ||\n          0\n        const pFCPUFDest =\n          (imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['pFCPUFDest']) ||\n          0\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n\n        const vIPIItem = ipi\n          ? parseFloat(ipi['vIPI'] / parseFloat(produto['qCom']) || 0)\n          : 0\n        const vDescItem =\n          parseFloat(produto['vDesc'] / parseFloat(produto['qCom'])) || 0\n        const vFreteItem = produto['vFrete']\n          ? parseFloat(\n              (produto['vFrete'] / parseFloat(produto['qCom'])).toFixed(6)\n            )\n          : 0\n        // Verificar se é devolução\n        const isDevolucao = devolucaoCFOPs.includes(produto['CFOP'])\n        const PUNITCONT = parseFloat(\n          (\n            parseFloat(produto['vUnCom']) +\n            (isDevolucao ? vIPIDevol : vIPIItem) -\n            vDescItem\n          ).toFixed(3)\n        )\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n\n        // Calcular os valores conforme as fórmulas fornecidas\n        const vICMSSubstituto = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSSubstituto']) || 0) / produto['qCom']\n        )\n\n        const vBCST = parseFloat(\n          ((icms && icms[icmsObj]?.['vBCST']) || 0) / produto['qCom']\n        )\n\n        const vICMSST = parseFloat(\n          ((icms && icms[icmsObj]?.['vICMSST']) || 0) / produto['qCom']\n        )\n\n        const pICMSST = parseFloat((icms && icms[icmsObj]?.['pICMSST']) || 0)\n\n        const pMVAST = parseFloat((icms && icms[icmsObj]?.['pMVAST']) || 0)\n\n        // BC de IPI  = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(produto['vProd']) / parseFloat(produto['qCom'])\n\n        const VLICMSPARTDEST = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFDest']) ||\n            0) / produto['qCom']\n        )\n\n        const VLICMSPARTREM = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vICMSUFRemet']) ||\n            0) / produto['qCom']\n        )\n\n        const VLFCPPART = parseFloat(\n          ((imposto &&\n            imposto['ICMSUFDest'] &&\n            imposto['ICMSUFDest']['vFCPUFDest']) ||\n            0) / produto['qCom']\n        )\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis\n          ? pis['CST'] ||\n            pis['PISAliq']?.['CST'] ||\n            pis['PISOutr']?.['CST'] ||\n            '00'\n          : cofins\n          ? cofins['CST'] ||\n            cofins['COFINSAliq']?.['CST'] ||\n            cofins['COFINSOutr']?.['CST'] ||\n            '00'\n          : '00'\n        const xPed = produto['xPed'] || ''\n        // Adicione o resultado ao array 'results'\n        const pICMS = icms ? parseFloat(icms[icmsObj]?.['pICMS']) : 0\n        const resultItem = {\n          nItem: nItem,\n          cProd: produto['cProd'],\n          xProd: produto['xProd'],\n          ncm: produto['NCM'],\n          CODCEST: produto['CEST'] || '',\n          cEAN: produto['cEAN'],\n          uCom: produto['uCom'],\n          qCom: produto['qCom'],\n          vUnCom: produto['vUnCom'],\n          vProd: produto['vProd'],\n          vProdTotal: vProdTotal,\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          natOp,\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vOutro: vOutro,\n          vFreteItem: vFreteItem,\n          vFreteTotal: vFreteTotal,\n          vDesc:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vDesc'],\n          vDescItem: vDescItem,\n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          ufDest:\n            xmlData['nfeProc']['NFe']['infNFe']['dest']['enderDest']['UF'],\n          ufEmit:\n            xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          pPIS: pis ? pis['pPIS'] || 0 : 0,\n          vPIS: pis ? parseFloat(pis['vPIS'] / produto['qCom']) || 0 : 0,\n          pCOFINS: cofins ? cofins['pCOFINS'] || 0 : 0,\n          vCOFINS: cofins\n            ? parseFloat(cofins['vCOFINS'] / produto['qCom']) || 0\n            : 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vIPIItem,\n          CODSITTRIBIPI: ipi ? ipi['CST'] || 0 : 0,\n          //VLBASEIPI items\n          vBC_IPI: imposto?.['IPI']?.['IPITrib']?.['vBC']\n            ? parseFloat(imposto['IPI']['IPITrib']['vBC'] / produto['qCom'])\n            : 0,\n          vBC_PIS: pis ? pis['vBC'] || 0 : 0,\n          vBC_COFINS: cofins ? cofins['vBC'] || 0 : 0,\n          vICMS: parseFloat(icms && icms[icmsObj]?.['vICMS']) || 0,\n          pICMS: pICMS,\n          CST: icms ? parseFloat(icms[icmsObj]?.['CST']) || '00' : '00',\n          CST_PIS_COFINS, // Variável única para CST\n\t\t\t\t\t// remova o vlfreteitem da PCMOVCOMPLETE nas devoluções para não duplicar o valor de frete na BASEICMS do livro fiscal\n          BASEICMS: parseFloat((icms && icms[icmsObj]?.['vBC']) ? parseFloat(icms[icmsObj]['vBC']) / parseFloat(produto['qCom']) : 0).toFixed(6),\n          STBCR: parseFloat(\n            ((icms && icms[icmsObj]?.['vICMSSTRet']) || 0) / produto['qCom']\n          ),\n          VLICMSBCR: vICMSSubstituto,\n          BASEICST: vBCST,\n          pST: icms ? parseFloat(icms[icmsObj]?.['pST']) || 0 : 0,\n          ST: vICMSST,\n          PERCST: pICMSST,\n          IVA: pMVAST,\n          VLBASEIPI: VLBASEIPI || 0,\n          vBCUFDest: vBCUFDest,\n          pICMSUFDest: pICMSUFDest,\n          pICMSInter: pICMSInter,\n          pICMSInterPart: pICMSInterPart,\n          VLICMSPARTDEST: VLICMSPARTDEST,\n          VLICMSPARTREM: VLICMSPARTREM,\n          pFCPUFDest: pFCPUFDest,\n          VLFCPPART: VLFCPPART,\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          PUNITCONT: PUNITCONT,\n          VLBASEPISCOFINS: isDevolucao\n            ? imposto?.['PIS']?.['PISOutr']?.['vBC']\n              ? parseFloat(imposto['PIS']['PISOutr']['vBC'])\n              : imposto?.['COFINS']?.['COFINSOutr']?.['vBC']\n              ? parseFloat(imposto['COFINS']['COFINSOutr']['vBC'])\n              : 0\n            : imposto?.['PIS']?.['PISAliq']?.['vBC']\n            ? parseFloat(imposto['PIS']['PISAliq']['vBC'])\n            : imposto?.['COFINS']?.['COFINSAliq']?.['vBC']\n            ? parseFloat(imposto['COFINS']['COFINSAliq']['vBC'])\n            : 0,\n          xPed,\n          vIPIDevol\n        }\n\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "cc48f450-5540-415a-a9e9-751137271bfb", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-4620, 8140], "alwaysOutputData": false}, {"parameters": {"content": "## PATH BASE TESTE\n//192.168.1.245/xml_mimo/TESTE/*.xml", "height": 298.9096373748045, "width": 350.64252336448556, "color": 4}, "id": "90af79a6-9cce-4b60-b49e-c5eebfd45a74", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-5100, 8020]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "768583e1-d878-491d-909f-c25dda02d99b", "name": "Entrada/Saida", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-4300, 8140]}, {"parameters": {"content": "## Not<PERSON> de Vendas", "height": 688.9653742119233, "width": 1297.262738596667, "color": 2}, "id": "5864ef07-e9a7-466f-bbb4-b95e1a6ddd54", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3760, 7000]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "DTREGISTRO", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pST\"] }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBCUFDest\"] }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSUFDest\"] }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInter\"] }}"}, {"name": "PERCPROVPART", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInterPart\"] }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTDEST\"] }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTREM\"] }}"}, {"name": "ALIQFCP", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pFCPUFDest\"] }}"}, {"name": "VLFCPPART", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLFCPPART\"] }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "CODPRODGNRE", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "EANCODPROD", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODCEST", "value": "={{ $('CUSTOS3').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODCEST\"] }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "0"}, {"name": "VLBASEOUTROS", "value": "0"}]}, "options": {}}, "id": "277f6705-2501-4a85-961e-b7fd3187b622", "name": "IMPOSTOS COMPLE4", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 8520], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT NUMTRANSENT\n  FROM PCNFENT N\n WHERE CHAVENFE = '{{ $json[\"CHAVENFE\"] }}'\nAND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = N.NUMTRANSENT\n          AND ROWNUM = 1)", "limit": 1, "options": {"includePreviousData": true}}, "id": "4e8fd52d-3657-47cc-99f4-b1cf2937b6e4", "name": "NUMTRANS6", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3380, 8700], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "47deffe1-d87f-4880-bd29-3a357849592b", "name": "Loop Over Items4", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-3380, 8500]}, {"parameters": {"query": "=WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.<PERSON>,\n           E.CUSTOREAL,\n           E.<PERSON>TOREP,\n           E.CUSTOCONT,\n           ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * C.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'\n        AND T.UFDESTINO = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n        AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * T.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n    AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}\n    AND E.CODFILIAL = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "limit": 1, "options": {"includePreviousData": true}}, "id": "c2812c4f-8aea-4fba-a919-6270a94a00e3", "name": "CUSTOS3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3040, 8520], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "CODOPER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODOPER\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "DTMOVLOG", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') + \" \" + $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "NUMTRANSVENDA"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"NUMTRANSENT\"] }}"}, {"name": "CODFORNEC", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODFORNEC\"] }}"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO", "value": "N"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "NUMSEQ", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODPROD", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "CODAUXILIAR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "NBM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ncm\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "UNIDADE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"uCom\"].slice(0,2) }}"}, {"name": "QT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "QTCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "PUNIT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "PUNITCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PUNITCONT\"] }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "VLDESCONTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vDescItem\"] }}"}, {"name": "PERCICM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMS\"] | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vFreteItem\"] }}"}, {"name": "BASEICMS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "BASEICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "BASEBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "STBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"STBCR\"] }}"}, {"name": "VLICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSBCR\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "ST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ST\"] }}"}, {"name": "PERCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PERCST\"] }}"}, {"name": "IVA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"IVA\"] }}"}, {"name": "PERPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pPIS\"] }}"}, {"name": "VLCREDPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBC_PIS\"] }}"}, {"name": "PERCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pCOFINS\"] }}"}, {"name": "VLCREDCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vIPIItem\"] }}"}, {"name": "PERCIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pIPI\"] }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "NUMLOTE", "value": "1"}, {"name": "CODICMTAB", "value": "={{ $json[\"CODICMTAB\"] }}"}, {"name": "CODST", "value": "={{ $json[\"CODST\"] }}"}, {"name": "CUSTOFIN", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "CUSTOREAL", "value": "={{ $json[\"CUSTOREAL\"] }}"}, {"name": "CUSTOREP", "value": "={{ $json[\"CUSTOREP\"] }}"}, {"name": "CUSTOCONT", "value": "={{ $json[\"CUSTOCONT\"] }}"}, {"name": "CUSTOFINEST", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "PTABELA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "CODDEVOL", "value": "={{ $json.previousData.previousData.CODDEVOL }}"}, {"name": "CALCCREDIPI", "value": "N"}]}, "options": {}}, "id": "f5edfc74-74dc-4908-ac88-1ac2ca454244", "name": "IMPOSTOS3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2880, 8520], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT E.NUM<PERSON>ANSENT, \n       <PERSON><PERSON>CHAVENF<PERSON>, \n       <PERSON><PERSON>CODFIL<PERSON>L,\n       E.<PERSON><PERSON>,\n      (SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"CGCFILIAL\"] }}') AS CODCLI,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"CGCFILIAL\"] }}') AS CODFORNEC,\n      CASE WHEN E.TIPODESCARGA = 'R' THEN 'ER' WHEN E.TIPODESCARGA = '6' THEN 'ED' END CODOPER,\n      CASE WHEN TIPODESCARGA = '6' THEN '{{ $json[\"CODDEVOL\"] }}' ELSE NULL END CODDEVOL\n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT NUMTRANSENT FROM PCMOV WHERE NUMTRANSENT = E.NUMTRANSENT AND ROWNUM = 1)\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "limit": 1, "options": {"includePreviousData": true}}, "id": "198f2b34-3f41-4ea7-a6b1-9cbb37153328", "name": "NUMTRANS7", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3700, 8480], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "limit": 1, "options": {"includePreviousData": true}}, "id": "ad6c8348-89f6-46f4-8b28-9e5c25d2b649", "name": "NUMTRANSITEM3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3200, 8520], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "NUMTRANSENT", "options": {"removeOtherFields": true}}, "id": "af9c9fbb-56f0-454b-85cb-f2ea681a34ff", "name": "Remove Duplicates3", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3040, 8720]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "5ea90523-df0a-4609-8683-febe56bb185b", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "f5da2796-160f-4e23-be21-bba7bfde21a9", "name": "If4", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3560, 8480]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "5ea90523-df0a-4609-8683-febe56bb185b", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "fe1b5976-9f21-4854-a647-ac52e9a3f330", "name": "If5", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3200, 8700]}, {"parameters": {"content": "## Notas de Entrada\n- Campo: CALCCREDIPI = N | grava o VLIPI em campo vl.Outras IPI no livro fiscal", "height": 572.0735231700221, "width": 1299.3175581946332, "color": 4}, "id": "12c5de0c-58e8-4a46-965e-2574aef63421", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3760, 8400]}, {"parameters": {"query": "=SELECT N.NUMTRANSVENDA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $json[\"previousData\"][\"chNFe\"] }}'\n   AND EXISTS (SELECT NUMTRANSVENDA\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = N.NUMTRANSVENDA\n          AND ROWNUM = 1)", "limit": 1, "options": {"includePreviousData": true}}, "id": "c17e90a0-ed73-4162-8be7-ce46abdec2d3", "name": "NUMTRANS1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3380, 7300], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.not_found }}", "operation": "contains", "value2": "=true"}]}}, "id": "1bbbcd11-8e6b-4ac5-b767-2a64d438ffb9", "name": "IF", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3200, 7300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.not_found }}", "operation": "contains", "value2": "=true"}]}}, "id": "c0be7265-4191-4927-9b5c-1c37c67b7665", "name": "IF1", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3560, 7080]}, {"parameters": {"query": "=WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.<PERSON>,\n           E.CUSTOREAL,\n           E.<PERSON>TOREP,\n           E.CUSTOCONT,\n           ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * C.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'\n        AND T.UFDESTINO = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n        AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * T.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n    AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}\n    AND E.CODFILIAL = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "limit": 1, "options": {"includePreviousData": true}}, "id": "f13ddb63-97e7-4a9f-85e9-63a41beaf3fc", "name": "CUSTOS", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3040, 7120], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "NUMSEQ", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODOPER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODOPER\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "DTMOVLOG", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') + \" \" + $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "1"}, {"name": "CODCLI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"NUMTRANSVENDA\"] }}"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT"}, {"name": "CODFORNEC", "value": "=(SELECT CODFORNEC FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO", "value": "N"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "CODPROD", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "CODAUXILIAR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "NBM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ncm\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "UNIDADE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"uCom\"].slice(0,2) }}"}, {"name": "QT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "QTCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "PUNIT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "PUNITCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PUNITCONT\"] }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "VLDESCONTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vDescItem\"] }}"}, {"name": "PERCICM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMS\"] | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vFreteItem\"] }}"}, {"name": "BASEICMS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "BASEICMSBCR", "value": "0"}, {"name": "BASEBCR", "value": "0"}, {"name": "STBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"STBCR\"] }}"}, {"name": "VLICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSBCR\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "ST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ST\"] }}"}, {"name": "PERCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PERCST\"] }}"}, {"name": "IVA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"IVA\"] }}"}, {"name": "PERPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pPIS\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLBASEPISCOFINS\"] }}"}, {"name": "PERCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pCOFINS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vIPIItem\"] }}"}, {"name": "PERCIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pIPI\"] }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBC_IPI\"] }}"}, {"name": "NUMLOTE"}, {"name": "CODICMTAB", "value": "={{ $json[\"CODICMTAB\"] }}"}, {"name": "CODST", "value": "={{ $json[\"CODST\"] }}"}, {"name": "PTABELA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "CUSTOFIN", "value": "={{ $json[\"CMV\"] }}"}, {"name": "CUSTOREAL", "value": "={{ $json[\"CMV\"] }}"}, {"name": "CUSTOREP", "value": "={{ $json[\"CUSTOREP\"] }}"}, {"name": "CUSTOCONT", "value": "={{ $json[\"CUSTOCONT\"] }}"}, {"name": "CUSTOFINEST", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "CODUSUR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODRCA\"] }}"}, {"name": "NUMPED", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"xPed\"] | 0 }}"}, {"name": "NUMREGIAO", "value": "=(SELECT NVL(REGCLI.NUMREGIAO, P.NUMRE<PERSON>) NUMREGIAO\n  FROM PCCLIENT C,\n       PCPRACA P,\n       (SELECT PCTABPRCLI.CODCLI,\n               PCTABPRCLI.NUMREGIAO\n           FROM PCTABPRCLI,\n                <PERSON>REGIAO\n           WHERE PCTABPRCLI.NUMREGIAO = PCREGIAO.NUMREGIAO\n             AND PCTABPRCLI.CODFILIALNF = '{{ $json[\"CODFILIAL\"] }}') REGCLI\n  WHERE C.CODPRACA = P.CODPRACA\n    AND C.CODCLI = REGCLI.CODCLI (+)\n    AND TO_CHAR(C.CODCLI) = '{{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}')"}, {"name": "CODEPTO", "value": "=(SELECT CODEPTO FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "CODSEC", "value": "=(SELECT CODSEC FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "DATAESTOQUE"}]}, "options": {}}, "id": "af5ed883-2b36-46c3-88ed-54f6de5705d8", "name": "IMPOSTOS", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2880, 7120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT S.CODCLI,\n       S.<PERSON>UMTRANSVENDA,\n       S.CODFILIAL,\n       CASE WHEN TIPOVENDA = '1' THEN 'S' \n            WHEN TIPOVENDA = 'SR' THEN 'SR' \n        END CODOPER\n  FROM PCNFSAID S\n  WHERE S.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n    AND S.DTCANCEL IS NULL\n    AND NOT EXISTS (SELECT NUMTRANSVENDA FROM PCMOV WHERE NUMTRANSVENDA = S.NUMTRANSVENDA AND ROWNUM = 1)\n    AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "limit": 1, "options": {"includePreviousData": true}}, "id": "7cfe210b-2070-4e40-b8ec-35ad23482f47", "name": "NUMTRANS", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3700, 7080], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "limit": 1, "options": {"includePreviousData": true}}, "id": "1374e9ee-4658-429d-a0a3-cb62685b47d0", "name": "NUMTRANSITEM", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3200, 7120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "NUMTRANSVENDA", "options": {"removeOtherFields": true}}, "id": "38bf8156-7fa1-44a3-bca4-e663d7b21ed5", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3020, 7320]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "DTREGISTRO", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }} {{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pST\"] }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBCUFDest\"] }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSUFDest\"] }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInter\"] }}"}, {"name": "PERCPROVPART", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInterPart\"] }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTDEST\"] }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTREM\"] }}"}, {"name": "ALIQFCP", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pFCPUFDest\"] }}"}, {"name": "VLFCPPART", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLFCPPART\"] }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "CODPRODGNRE", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "EANCODPROD", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODCEST", "value": "={{ $('CUSTOS').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODCEST\"] }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "0"}, {"name": "VLBASEOUTROS", "value": "0"}]}, "options": {}}, "id": "fd0c0702-2300-4573-8b43-1c7ed8ecde9d", "name": "IMPOSTOS COMPLE", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 7120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "e3cdb19f-090c-492d-b749-dff84313e279", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-3380, 7100]}, {"parameters": {"options": {}}, "id": "13df2770-6460-46db-9cd1-dc57c9dbcda2", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2860, 7480]}, {"parameters": {"query": "=SELECT ROUND(SUM(CUSTOFIN * QT), 2) AS VLCUSTOFIN,\n       ROUND(SUM(CUSTOREAL * QT), 2) AS VLCUSTOREAL,\n       ROUND(SUM(CUSTOREP * QT), 2) AS VLCUSTOREP,\n       ROUND(SUM(CUSTOCONT * QT), 2) AS VLCUSTOCONT\n  FROM PCMOV\n WHERE PCMOV.NUMTRANSVENDA = {{ $json[\"NUMTRANSVENDA\"] }}", "limit": 1, "options": {"includePreviousData": true}}, "id": "b5dc72b9-1273-495d-a375-27ecf255fdba", "name": "VLCUSTOS", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2860, 7320], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCNFSAID", "mode": "name"}, "set": {"string": [{"name": "VLCUSTOFIN", "value": "={{ $json[\"VLCUSTOFIN\"] }}"}, {"name": "VLCUSTOREAL", "value": "={{ $json[\"VLCUSTOREAL\"] }}"}, {"name": "VLCUSTOREP", "value": "={{ $json[\"VLCUSTOREP\"] }}"}, {"name": "VLCUSTOCONT", "value": "={{ $json[\"VLCUSTOCONT\"] }}"}]}, "values": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $json[\"previousData\"][\"NUMTRANSVENDA\"] }}"}]}, "options": {}}, "id": "483b1295-c77b-42ad-b47c-3570370e3c17", "name": "CUSTOS NFSAID", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2700, 7320], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "48061011-603a-4df2-8291-1661d1171c19", "name": "Loop Over Items2", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2880, 8720]}, {"parameters": {"query": "=DECLARE\n    RETORNO NUMBER := {{ $json[\"NUMTRANSENT\"] }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.COMPRAS_ENTRADA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "limit": null, "options": {"includePreviousData": false}}, "id": "e867867a-7959-40f3-95ce-d52a8931379f", "name": "Atualizar Estoque - PKG_ESTOQUE1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 8740], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"query": "=DECLARE\n    RETORNO NUMBER := {{ $json[\"NUMTRANSVENDA\"] }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.VENDAS_SAIDA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "limit": null, "options": {"includePreviousData": false}}, "id": "047fa483-7072-4e73-8c73-a3c588e92d4a", "name": "Atualizar Estoque - PKG_ESTOQUE", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2700, 7500], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "340", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "79", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "6806b89e-4d57-405f-ae3e-0403945e817c", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-4460, 8140]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "venda", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "3a31ad41-3aec-44d6-a83e-6f9b2cbc00ee", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3940, 7420]}, {"parameters": {"content": "## <PERSON><PERSON> Saídas - Simples Remessa - 1322\n- Cancelar via rotina 1326", "height": 688.9653742119233, "width": 1297.262738596667, "color": 5}, "id": "c99c2ee7-7edb-4fc1-8732-d8b3d77fd0a8", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3760, 7700]}, {"parameters": {"query": "=SELECT N.NUMTRANSVENDA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $json[\"previousData\"][\"chNFe\"] }}'\n   AND EXISTS (SELECT NUMTRANSVENDA\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = N.NUMTRANSVENDA\n          AND ROWNUM = 1)", "limit": 1, "options": {"includePreviousData": true}}, "id": "479954e5-92b6-4baf-8b4c-c6a70a146056", "name": "NUMTRANS2", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3380, 8000], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.not_found }}", "operation": "contains", "value2": "=true"}]}}, "id": "fd1eca75-f81d-4241-ba2f-b2ecdddf617b", "name": "IF2", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3200, 8000]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.not_found }}", "operation": "contains", "value2": "=true"}]}}, "id": "e0a27634-4fef-410d-9165-be0b5219066b", "name": "IF3", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3560, 7780]}, {"parameters": {"query": "=WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.<PERSON>,\n           E.CUSTOREAL,\n           E.<PERSON>TOREP,\n           E.CUSTOCONT,\n           ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * C.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'\n        AND T.UFDESTINO = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n        AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * T.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n    AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}\n    AND E.CODFILIAL = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "limit": 1, "options": {"includePreviousData": true}}, "id": "4f00b093-148a-4eed-839a-3b1241de7081", "name": "CUSTOS1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3040, 7820], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "NUMSEQ", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODOPER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODOPER\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "DTMOVLOG", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') + \" \" + $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "1"}, {"name": "CODCLI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"NUMTRANSVENDA\"] }}"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT"}, {"name": "CODFORNEC", "value": "=(SELECT CODFORNEC FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "CODPROD", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "CODAUXILIAR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "NBM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ncm\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "UNIDADE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"uCom\"].slice(0,2) }}"}, {"name": "QT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "QTCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "PUNIT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "PUNITCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PUNITCONT\"] }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "VLDESCONTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vDescItem\"] }}"}, {"name": "PERCICM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMS\"] | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vFreteItem\"] }}"}, {"name": "BASEICMS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "BASEICMSBCR", "value": "0"}, {"name": "BASEBCR", "value": "0"}, {"name": "STBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"STBCR\"] }}"}, {"name": "VLICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSBCR\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "ST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ST\"] }}"}, {"name": "PERCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PERCST\"] }}"}, {"name": "IVA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"IVA\"] }}"}, {"name": "PERPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pPIS\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLBASEPISCOFINS\"] }}"}, {"name": "PERCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pCOFINS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vIPIItem\"] }}"}, {"name": "PERCIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pIPI\"] }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBC_IPI\"] }}"}, {"name": "NUMLOTE"}, {"name": "CODICMTAB", "value": "={{ $json[\"CODICMTAB\"] }}"}, {"name": "CODST", "value": "={{ $json[\"CODST\"] }}"}, {"name": "PTABELA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "CUSTOFIN", "value": "={{ $json[\"CMV\"] }}"}, {"name": "CUSTOREAL", "value": "={{ $json[\"CMV\"] }}"}, {"name": "CUSTOREP", "value": "={{ $json[\"CUSTOREP\"] }}"}, {"name": "CUSTOCONT", "value": "={{ $json[\"CUSTOCONT\"] }}"}, {"name": "CUSTOFINEST", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "CODUSUR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODRCA\"] }}"}, {"name": "NUMPED", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"xPed\"] | 0 }}"}, {"name": "NUMREGIAO", "value": "=(SELECT NVL(REGCLI.NUMREGIAO, P.NUMRE<PERSON>) NUMREGIAO\n  FROM PCCLIENT C,\n       PCPRACA P,\n       (SELECT PCTABPRCLI.CODCLI,\n               PCTABPRCLI.NUMREGIAO\n           FROM PCTABPRCLI,\n                <PERSON>REGIAO\n           WHERE PCTABPRCLI.NUMREGIAO = PCREGIAO.NUMREGIAO\n             AND PCTABPRCLI.CODFILIALNF = '{{ $json[\"CODFILIAL\"] }}') REGCLI\n  WHERE C.CODPRACA = P.CODPRACA\n    AND C.CODCLI = REGCLI.CODCLI (+)\n    AND TO_CHAR(C.CODCLI) = '{{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}')"}, {"name": "CODEPTO", "value": "=(SELECT CODEPTO FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "CODSEC", "value": "=(SELECT CODSEC FROM PCPRODUT WHERE CODPROD={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "PERCBASERED", "value": "100"}, {"name": "PERCBASEREDST", "value": "100"}, {"name": "NUMSEQPED", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "APROVEITACREDPISCOFINS", "value": "S"}, {"name": "DATAESTOQUE"}]}, "options": {}}, "id": "ae4c996d-8296-4778-a4c8-ed548a18508e", "name": "IMPOSTOS1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2880, 7820], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT S.CODCLI,\n       S.<PERSON>UMTRANSVENDA,\n       S.CODFILIAL,\n       CASE WHEN TIPOVENDA = '1' THEN 'S' \n            WHEN TIPOVENDA = 'SR' THEN 'SR' \n        END CODOPER\n  FROM PCNFSAID S\n  WHERE S.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n    AND S.DTCANCEL IS NULL\n    AND NOT EXISTS (SELECT NUMTRANSVENDA FROM PCMOV WHERE NUMTRANSVENDA = S.NUMTRANSVENDA AND ROWNUM = 1)\n    AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "limit": 1, "options": {"includePreviousData": true}}, "id": "39cb2d54-a462-4c8b-8b95-93b070057275", "name": "NUMTRANS3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3700, 7780], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "limit": 1, "options": {"includePreviousData": true}}, "id": "ee9c2590-2282-4b75-8bf4-f47d56b75c51", "name": "NUMTRANSITEM1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3200, 7820], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "NUMTRANSVENDA", "options": {"removeOtherFields": true}}, "id": "6745e336-cba1-4ae9-802c-5f9d9b32a406", "name": "Remove Duplicates1", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3020, 8020]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "DTREGISTRO", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }} {{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pST\"] }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBCUFDest\"] }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSUFDest\"] }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInter\"] }}"}, {"name": "PERCPROVPART", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInterPart\"] }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTDEST\"] }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTREM\"] }}"}, {"name": "ALIQFCP", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pFCPUFDest\"] }}"}, {"name": "VLFCPPART", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLFCPPART\"] }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "CODPRODGNRE", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "EANCODPROD", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODCEST", "value": "={{ $('CUSTOS1').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODCEST\"] }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "0"}, {"name": "VLBASEOUTROS", "value": "0"}]}, "options": {}}, "id": "9950b422-abec-4b1a-bb0c-1cb9f0f7d018", "name": "IMPOSTOS COMPLE1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 7820], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "e4f911ce-a9eb-404a-96ac-222928d0417b", "name": "Loop Over Items3", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-3380, 7800]}, {"parameters": {"options": {}}, "id": "d4e95172-4480-42df-a312-972912dfca1c", "name": "Loop Over Items5", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2860, 8180]}, {"parameters": {"query": "=SELECT ROUND(SUM(CUSTOFIN * QT), 2) AS VLCUSTOFIN,\n       ROUND(SUM(CUSTOREAL * QT), 2) AS VLCUSTOREAL,\n       ROUND(SUM(CUSTOREP * QT), 2) AS VLCUSTOREP,\n       ROUND(SUM(CUSTOCONT * QT), 2) AS VLCUSTOCONT\n  FROM PCMOV\n WHERE PCMOV.NUMTRANSVENDA = {{ $json[\"NUMTRANSVENDA\"] }}", "limit": 1, "options": {"includePreviousData": true}}, "id": "632e4a6b-a3ed-4f7f-a213-5ac182b02090", "name": "VLCUSTOS1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2860, 8020], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCNFSAID", "mode": "name"}, "set": {"string": [{"name": "VLCUSTOFIN", "value": "={{ $json[\"VLCUSTOFIN\"] }}"}, {"name": "VLCUSTOREAL", "value": "={{ $json[\"VLCUSTOREAL\"] }}"}, {"name": "VLCUSTOREP", "value": "={{ $json[\"VLCUSTOREP\"] }}"}, {"name": "VLCUSTOCONT", "value": "={{ $json[\"VLCUSTOCONT\"] }}"}]}, "values": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $json[\"previousData\"][\"NUMTRANSVENDA\"] }}"}]}, "options": {}}, "id": "37d00ce9-c745-4605-bd95-9dccd7312d4f", "name": "CUSTOS NFSAID1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2700, 8020], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=DECLARE\n    RETORNO NUMBER := {{ $json[\"NUMTRANSVENDA\"] }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.VENDAS_SAIDA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "limit": {}, "options": {"includePreviousData": false}}, "id": "ab1530cb-5e2a-45a8-8d68-47a3a7296c63", "name": "Atualizar Estoque - PKG_ESTOQUE2", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2700, 8200], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOVCOMPLE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "DTREGISTRO", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "ALIQICMS1RET", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pST\"] }}"}, {"name": "VLBASEPARTDEST", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBCUFDest\"] }}"}, {"name": "ALIQINTERNADEST", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSUFDest\"] }}"}, {"name": "ALIQINTERORIGPART", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInter\"] }}"}, {"name": "PERCPROVPART", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMSInterPart\"] }}"}, {"name": "VLICMSPARTDEST", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTDEST\"] }}"}, {"name": "VLICMSPARTREM", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSPARTREM\"] }}"}, {"name": "ALIQFCP", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"pFCPUFDest\"] }}"}, {"name": "VLFCPPART", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLFCPPART\"] }}"}, {"name": "NUMCHAVEEXP", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "CODPRODGNRE", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "EANCODPROD", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "CODSITTRIBIPI"}, {"name": "CODTRIBPISCOFINS", "value": "7"}, {"name": "NITEMXML", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODCEST", "value": "={{ $('CUSTOS4').item.json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODCEST\"] }}"}, {"name": "ORIGMERCTRIB", "value": "1"}, {"name": "VLBASEFRETE", "value": "0"}, {"name": "VLBASEOUTROS", "value": "0"}]}, "options": {}}, "id": "3201d29f-94ee-4567-847c-95e1558c70d1", "name": "IMPOSTOS COMPLE5", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 9120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT NUMTRANSENT\n  FROM PCNFENT N\n WHERE CHAVENFE = '{{ $json[\"CHAVENFE\"] }}'\nAND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = N.NUMTRANSENT\n          AND ROWNUM = 1)", "limit": 1, "options": {"includePreviousData": true}}, "id": "e653756a-ecdf-4644-9c13-40d615860651", "name": "NUMTRANS8", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3380, 9300], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "f9232aa4-0f33-46a6-9888-af3b14539129", "name": "Loop Over Items6", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-3380, 9100]}, {"parameters": {"query": "=WITH UF AS (\n    /* Tributação do produto por UF */\n    SELECT T.CODFILIALNF AS CODFILIAL,\n           E.CODPROD,\n           T.CODST,\n           C.CODICMTAB,\n           C.CODICMTABPF,\n           E.<PERSON>,\n           E.CUSTOREAL,\n           E.<PERSON>TOREP,\n           E.CUSTOCONT,\n           ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * C.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n           NULL AS CODSTPARTILHA\n      FROM PCTABTRIB T,\n           PCTRIBUT C,\n           PCEST E,\n           PCCONSUM\n      WHERE T.CODST = C.CODST\n        AND E.CODFILIAL = T.CODFILIALNF\n        AND E.CODPROD = T.CODPROD\n        AND T.CODFILIALNF = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'\n        AND T.UFDESTINO = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n        AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}),\n  REGIAO AS (\n    /* Tributação do produto por Região */\n    SELECT DISTINCT T.CODST,\n       E.CODPROD,\n       T.CODICMTAB,\n       T.CODICMTABPF,\n       E.CUSTOFIN,\n       E.CUSTOREAL,\n       E.CUSTOREP,\n       E.CUSTOCONT,\n       E.CODFILIAL,\n       ((({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * T.CODICMTAB) / 100) + (({{ $json[\"previousData\"][\"previousData\"][\"vUnCom\"] }} * PCCONSUM.TXVENDA) / 100) + E.CUSTOFIN ) AS CMV,\n       /* Figura ICMS UF Destino - 514 */\n       TBP.CODSTPARTILHA\n  FROM PCTRIBUTPARTILHA TBP,\n       PCTRIBUT T,\n       PCEST E, \n       PCTABPR A,\n       PCREGIAO D,\n       PCFILIAL F,\n       PCCONSUM\n  WHERE T.CODST = TBP.CODST\n    AND A.CODST = T.CODST\n    AND A.NUMREGIAO = D.NUMREGIAO\n    AND A.CODPROD = E.CODPROD\n    AND D.CODFILIAL = E.CODFILIAL\n    AND F.NUMREGIAOPADRAO = D.NUMREGIAO\n    AND TBP.UF = '{{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}'\n    AND E.CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"cProd\"] }}\n    AND E.CODFILIAL = '{{ $json[\"previousData\"][\"CODFILIAL\"] }}'),\n  P_TRIB AS (SELECT PARAMFILIAL.OBTERCOMOVARCHAR2('CON_USATRIBUTACAOPORUF') AS V_TRIBUF\n      FROM DUAL)\nSELECT CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODFILIAL ELSE REGIAO.CODFILIAL END AS CODFILIAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODPROD ELSE REGIAO.CODPROD END AS CODPROD,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODST ELSE REGIAO.CODST END AS CODST,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTAB ELSE REGIAO.CODICMTAB END AS CODICMTAB,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODICMTABPF ELSE REGIAO.CODICMTABPF END AS CODICMTABPF,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOFIN ELSE REGIAO.CUSTOFIN END AS CUSTOFIN,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREAL ELSE REGIAO.CUSTOREAL END AS CUSTOREAL,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOREP ELSE REGIAO.CUSTOREP END AS CUSTOREP,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CUSTOCONT ELSE REGIAO.CUSTOCONT END AS CUSTOCONT,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CMV ELSE REGIAO.CMV END AS CMV,\n       CASE WHEN P_TRIB.V_TRIBUF = 'S' THEN UF.CODSTPARTILHA ELSE REGIAO.CODSTPARTILHA END AS CODSTPARTILHA\n  FROM P_TRIB\n    LEFT JOIN UF\n      ON P_TRIB.V_TRIBUF = 'S'\n    LEFT JOIN REGIAO\n      ON P_TRIB.V_TRIBUF != 'S'", "limit": 1, "options": {"includePreviousData": true}}, "id": "7a7ffc14-e642-4ff8-afb3-f51d2ac58789", "name": "CUSTOS4", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3040, 9120], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCMOV", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSITEM", "value": "={{ $json[\"previousData\"][\"NUMTRANSITEM\"] }}"}, {"name": "CODOPER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODOPER\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "DTMOVLOG", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') + \" \" + $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "DTMOV", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "NUMTRANSVENDA"}, {"name": "STATUS", "value": "AB"}, {"name": "NUMTRANSENT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"NUMTRANSENT\"] }}"}, {"name": "CODFORNEC", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODFORNEC\"] }}"}, {"name": "PERCDESC", "value": "0"}, {"name": "VLBONIFIC", "value": "0"}, {"name": "CODFILIALRETIRA", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "GERAICMSLIVROFISCAL", "value": "S"}, {"name": "COMPRACONSIGNADO", "value": "N"}, {"name": "MOVESTOQUECONTABIL", "value": "S"}, {"name": "MOVESTOQUEGERENCIAL", "value": "S"}, {"name": "NUMSEQ", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"nItem\"] }}"}, {"name": "CODPROD", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }}"}, {"name": "CODAUXILIAR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cEAN\"] | 0 }}"}, {"name": "NBM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ncm\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "UNIDADE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"uCom\"].slice(0,2) }}"}, {"name": "QT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "QTCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"qCom\"] }}"}, {"name": "PUNIT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "PUNITCONT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PUNITCONT\"] }}"}, {"name": "DESCRICAO", "value": "=(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = {{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"cProd\"] }})"}, {"name": "VLDESCONTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vDescItem\"] }}"}, {"name": "PERCICM", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pICMS\"] | 0 }}"}, {"name": "SITTRIBUT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CST\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vFreteItem\"] }}"}, {"name": "BASEICMS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "BASEICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "BASEBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "STBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"STBCR\"] }}"}, {"name": "VLICMSBCR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLICMSBCR\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "ST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"ST\"] }}"}, {"name": "PERCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"PERCST\"] }}"}, {"name": "IVA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"IVA\"] }}"}, {"name": "PERPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pPIS\"] }}"}, {"name": "VLCREDPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLBASEPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vBC_PIS\"] }}"}, {"name": "PERCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pCOFINS\"] }}"}, {"name": "VLCREDCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vIPIDevol\"] }}"}, {"name": "PERCIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"pIPI\"] }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "NUMLOTE", "value": "1"}, {"name": "CODICMTAB", "value": "={{ $json[\"CODICMTAB\"] }}"}, {"name": "CODST", "value": "={{ $json[\"CODST\"] }}"}, {"name": "CUSTOFIN", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "CUSTOREAL", "value": "={{ $json[\"CUSTOREAL\"] }}"}, {"name": "CUSTOREP", "value": "={{ $json[\"CUSTOREP\"] }}"}, {"name": "CUSTOCONT", "value": "={{ $json[\"CUSTOCONT\"] }}"}, {"name": "CUSTOFINEST", "value": "={{ $json[\"CUSTOFIN\"] }}"}, {"name": "PTABELA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"vUnCom\"] }}"}, {"name": "CODDEVOL", "value": "={{ $json.previousData.previousData.CODDEVOL }}"}, {"name": "CALCCREDIPI", "value": "S"}, {"name": "CODUSUR", "value": "={{ $json[\"previousData\"][\"previousData\"][\"previousData\"][\"CODRCA\"] }}"}]}, "options": {}}, "id": "7ff56d2e-6752-4c48-8817-9dfc8b9cc867", "name": "IMPOSTOS4", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2880, 9120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT DFSEQ_PCMOVCOMPLE.NEXTVAL AS NUMTRANSITEM FROM DUAL", "limit": 1, "options": {"includePreviousData": true}}, "id": "094af438-1645-4c52-84ef-e9d19f73b618", "name": "NUMTRANSITEM4", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3200, 9120], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "NUMTRANSENT", "options": {"removeOtherFields": true}}, "id": "de9f2e5b-ae7f-4ab0-bc88-559090db4123", "name": "Remove Duplicates4", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3040, 9320]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "5ea90523-df0a-4609-8683-febe56bb185b", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "78f14784-081f-4f1d-ae4e-028f96224187", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3560, 9080]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "5ea90523-df0a-4609-8683-febe56bb185b", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "46638313-9cc1-420e-bfd0-b0fa7cfed363", "name": "If6", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3200, 9300]}, {"parameters": {"content": "## Notas de Devolução\n- Campo: CALCCREDIPI = S | grava o VLIPI em campo próprio no livro fiscal", "height": 572.0735231700221, "width": 1299.3175581946332}, "id": "ec9133a8-996e-4ef3-9de5-e33ef2d7b571", "name": "Sticky Note4", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3760, 9000]}, {"parameters": {"options": {}}, "id": "40ec7c78-c31e-432f-b745-946ab260fccb", "name": "Loop Over Items7", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2880, 9320]}, {"parameters": {"query": "=DECLARE\n    RETORNO NUMBER := {{ $json[\"NUMTRANSENT\"] }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.COMPRAS_ENTRADA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "limit": {}, "options": {"includePreviousData": false}}, "id": "ab4e55f2-fd45-47be-b2ef-a6139a1ed005", "name": "Atualizar Estoque - PKG_ESTOQUE3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2720, 9340], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "entrada", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "811aaf36-d237-4abf-a91a-ec6dde9deb17", "name": "natOp1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-3960, 8760]}, {"parameters": {"query": "=SELECT E.NUM<PERSON>ANSENT, \n       <PERSON><PERSON>CHAVENF<PERSON>, \n       <PERSON><PERSON>CODFIL<PERSON>L,\n       E.<PERSON><PERSON>,\n      (SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"CGCFILIAL\"] }}') AS CODCLI,\n      (SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"CGCFILIAL\"] }}') AS CODFORNEC,\n      CASE WHEN E.TIPODESCARGA = 'R' THEN 'ER' WHEN E.TIPODESCARGA = '6' THEN 'ED' END CODOPER,\n      CASE WHEN TIPODESCARGA = '6' THEN '{{ $json[\"CODDEVOL\"] }}' ELSE NULL END CODDEVOL\n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT NUMTRANSENT FROM PCMOV WHERE NUMTRANSENT = E.NUMTRANSENT AND ROWNUM = 1)\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "limit": 1, "options": {"includePreviousData": true}}, "id": "35f09326-4411-420e-8ed6-6065d8ae1378", "name": "NUMTRANS9", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-3700, 9080], "alwaysOutputData": false, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2500, 7520], "id": "3566b531-2e6c-4bcb-8f6f-6376c7861b84", "name": "Notificar <PERSON>", "webhookId": "66297a18-0314-44b7-abbc-096db207c2ec", "credentials": {"telegramApi": {"id": "UNvruqAYpXoS4kMJ", "name": "NERVSFLOW - bot"}}}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2500, 8220], "id": "1db658cb-f055-45a0-8d11-82557fd9da44", "name": "Notificar Error1", "webhookId": "b5b37cde-e0c7-491b-ae79-6ea431c44647", "credentials": {"telegramApi": {"id": "UNvruqAYpXoS4kMJ", "name": "NERVSFLOW - bot"}}}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2520, 8760], "id": "76f0ece4-635b-4a38-9bd1-cc05c9123bfe", "name": "Notificar Error2", "webhookId": "82c9e708-2756-4065-b250-8ac485b91b79", "credentials": {"telegramApi": {"id": "UNvruqAYpXoS4kMJ", "name": "NERVSFLOW - bot"}}}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-2520, 9360], "id": "19c47519-d17c-4beb-8e02-7fd6d6ee7c0d", "name": "Notificar Error3", "webhookId": "e9b076b9-1130-4804-909b-2071c0654941", "credentials": {"telegramApi": {"id": "UNvruqAYpXoS4kMJ", "name": "NERVSFLOW - bot"}}}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML no P:/", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Ler XML no P:/": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Entrada/Saida": {"main": [[{"node": "natOp", "type": "main", "index": 0}], [{"node": "natOp1", "type": "main", "index": 0}]]}, "IMPOSTOS COMPLE4": {"main": [[{"node": "Loop Over Items4", "type": "main", "index": 0}]]}, "NUMTRANS6": {"main": [[{"node": "If5", "type": "main", "index": 0}]]}, "Loop Over Items4": {"main": [[], [{"node": "NUMTRANSITEM3", "type": "main", "index": 0}]]}, "CUSTOS3": {"main": [[{"node": "IMPOSTOS3", "type": "main", "index": 0}]]}, "IMPOSTOS3": {"main": [[{"node": "IMPOSTOS COMPLE4", "type": "main", "index": 0}]]}, "NUMTRANS7": {"main": [[{"node": "If4", "type": "main", "index": 0}]]}, "NUMTRANSITEM3": {"main": [[{"node": "CUSTOS3", "type": "main", "index": 0}]]}, "Remove Duplicates3": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "If4": {"main": [[], [{"node": "Loop Over Items4", "type": "main", "index": 0}, {"node": "NUMTRANS6", "type": "main", "index": 0}]]}, "If5": {"main": [[], [{"node": "Remove Duplicates3", "type": "main", "index": 0}]]}, "NUMTRANS1": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "IF": {"main": [[], [{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "IF1": {"main": [[], [{"node": "NUMTRANS1", "type": "main", "index": 0}, {"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "CUSTOS": {"main": [[{"node": "IMPOSTOS", "type": "main", "index": 0}]]}, "IMPOSTOS": {"main": [[{"node": "IMPOSTOS COMPLE", "type": "main", "index": 0}]]}, "NUMTRANS": {"main": [[{"node": "IF1", "type": "main", "index": 0}]]}, "NUMTRANSITEM": {"main": [[{"node": "CUSTOS", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}, {"node": "VLCUSTOS", "type": "main", "index": 0}]]}, "IMPOSTOS COMPLE": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[], [{"node": "NUMTRANSITEM", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Atualizar Estoque - PKG_ESTOQUE", "type": "main", "index": 0}]]}, "VLCUSTOS": {"main": [[{"node": "CUSTOS NFSAID", "type": "main", "index": 0}]]}, "Loop Over Items2": {"main": [[], [{"node": "Atualizar Estoque - PKG_ESTOQUE1", "type": "main", "index": 0}]]}, "Atualizar Estoque - PKG_ESTOQUE1": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}], [{"node": "Notificar Error2", "type": "main", "index": 0}]]}, "Atualizar Estoque - PKG_ESTOQUE": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "Notificar <PERSON>", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Entrada/Saida", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "NUMTRANS", "type": "main", "index": 0}], [{"node": "NUMTRANS3", "type": "main", "index": 0}]]}, "NUMTRANS2": {"main": [[{"node": "IF2", "type": "main", "index": 0}]]}, "IF2": {"main": [[], [{"node": "Remove Duplicates1", "type": "main", "index": 0}]]}, "IF3": {"main": [[], [{"node": "NUMTRANS2", "type": "main", "index": 0}, {"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "CUSTOS1": {"main": [[{"node": "IMPOSTOS1", "type": "main", "index": 0}]]}, "IMPOSTOS1": {"main": [[{"node": "IMPOSTOS COMPLE1", "type": "main", "index": 0}]]}, "NUMTRANS3": {"main": [[{"node": "IF3", "type": "main", "index": 0}]]}, "NUMTRANSITEM1": {"main": [[{"node": "CUSTOS1", "type": "main", "index": 0}]]}, "Remove Duplicates1": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}, {"node": "VLCUSTOS1", "type": "main", "index": 0}]]}, "IMPOSTOS COMPLE1": {"main": [[{"node": "Loop Over Items3", "type": "main", "index": 0}]]}, "Loop Over Items3": {"main": [[], [{"node": "NUMTRANSITEM1", "type": "main", "index": 0}]]}, "Loop Over Items5": {"main": [[], [{"node": "Atualizar Estoque - PKG_ESTOQUE2", "type": "main", "index": 0}]]}, "VLCUSTOS1": {"main": [[{"node": "CUSTOS NFSAID1", "type": "main", "index": 0}]]}, "Atualizar Estoque - PKG_ESTOQUE2": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}], [{"node": "Notificar Error1", "type": "main", "index": 0}]]}, "IMPOSTOS COMPLE5": {"main": [[{"node": "Loop Over Items6", "type": "main", "index": 0}]]}, "NUMTRANS8": {"main": [[{"node": "If6", "type": "main", "index": 0}]]}, "Loop Over Items6": {"main": [[], [{"node": "NUMTRANSITEM4", "type": "main", "index": 0}]]}, "CUSTOS4": {"main": [[{"node": "IMPOSTOS4", "type": "main", "index": 0}]]}, "IMPOSTOS4": {"main": [[{"node": "IMPOSTOS COMPLE5", "type": "main", "index": 0}]]}, "NUMTRANSITEM4": {"main": [[{"node": "CUSTOS4", "type": "main", "index": 0}]]}, "Remove Duplicates4": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}]]}, "If": {"main": [[], [{"node": "Loop Over Items6", "type": "main", "index": 0}, {"node": "NUMTRANS8", "type": "main", "index": 0}]]}, "If6": {"main": [[], [{"node": "Remove Duplicates4", "type": "main", "index": 0}]]}, "Loop Over Items7": {"main": [[], [{"node": "Atualizar Estoque - PKG_ESTOQUE3", "type": "main", "index": 0}]]}, "Atualizar Estoque - PKG_ESTOQUE3": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}], [{"node": "Notificar Error3", "type": "main", "index": 0}]]}, "natOp1": {"main": [[{"node": "NUMTRANS7", "type": "main", "index": 0}], [{"node": "NUMTRANS9", "type": "main", "index": 0}]]}, "NUMTRANS9": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Notificar Error": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Notificar Error1": {"main": [[{"node": "Loop Over Items5", "type": "main", "index": 0}]]}, "Notificar Error2": {"main": [[{"node": "Loop Over Items2", "type": "main", "index": 0}]]}, "Notificar Error3": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "5fab609e97caf85a95cad132b5c4a20271d41f64fb86f9c1bf9200f21d684f36"}}