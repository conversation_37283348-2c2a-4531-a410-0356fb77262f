{"nodes": [{"parameters": {}, "id": "5c060462-43e0-448f-9e5b-2c6bf61dea4a", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-3880, 1600]}, {"parameters": {"options": {}}, "id": "83b28747-08dd-41f4-909a-ca99845af10f", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-3400, 1600]}, {"parameters": {"jsCode": "// 4-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)\n    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) \n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det['prod']\n        const imposto = det['imposto']\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][\n          'verProc'\n        ].includes('mercadolivre')\n          ? 'CML'\n          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(\n              'Bling'\n            )\n          ? 'D2C'\n          : ''\n        // BC de IPI = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        // Obter valores das novas tags\n        const xPed = produto['xPed'] || '';\n        const infAdProd = det['infAdProd'] || '';\n        const infCpl = xmlData['nfeProc']['NFe']['infNFe']['infAdic']?.['infCpl'] || '';\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',\n          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',\n          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],\n          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          vFrete: vFrete,\n          vOutro: vOutro, \n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],\n          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,\n          vICMS:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],\n          vProd:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],\n          BASEICST: parseFloat(\n            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']\n          ),\n          VLBASEIPI: VLBASEIPI,\n\t\t\t\t\t/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, \n          vIPI:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||\n            0,\n          vIPIDevol:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][\n              'vIPIDevol'\n            ] || 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vPIS: pis ? pis['vPIS'] || 0 : 0,\n          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,\n          cPais: dest['enderDest']['cPais'],\n          xPais: dest['enderDest']['xPais'],\n          CEP: dest['enderDest']['CEP'],\n          ufDest: dest['enderDest']['UF'],\n          xMun: dest['enderDest']['xMun'],\n          xLgr: dest['enderDest']['xLgr'],\n          nro: dest['enderDest']['nro'],\n          xBairro: dest['enderDest']['xBairro'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CODCOB,\n          CST_PIS_COFINS, \n          xPed,\n          infAdProd,\n          infCpl,\n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "a0885bec-30d2-4f7a-9ab7-9a7342f7449a", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3240, 1600]}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "id": "69aab75a-e8f8-4841-ae1b-88364aaab009", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3080, 1600]}, {"parameters": {"content": "## Regra Tributaria - finNFe - Finalidade de emissão da NF-e\n\n1 = NF-e normal.\n2 = NF-e complementar.\n3 = NF-e de ajuste.\n4 = Devolução de mercadoria.", "height": 209.7473416040146, "width": 388.85609947320665, "color": 6}, "id": "e93dae2e-7c9c-4e69-9ff4-6a510930f790", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3860, 1360]}, {"parameters": {"content": "## PATH BASE TESTE\n/u01/xml_mimo/mercado_livre/TESTE/*.xml", "height": 199.87149532710242, "width": 350.64252336448556, "color": 4}, "id": "ec62cdde-1734-44cb-b583-0904632a8e3a", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3440, 1360]}, {"parameters": {"assignments": {"assignments": [{"id": "705fc023-6be0-487d-8fb6-d0bd41074a2d", "name": "CODRCA", "value": "802", "type": "string"}, {"id": "845a4145-97c7-4523-b4c2-8e94907603d8", "name": "CODPRACA", "value": "331", "type": "string"}, {"id": "c82cf1d6-a69d-497c-8018-201b23e40424", "name": "CODSUPERVISOR", "value": "9", "type": "string"}, {"id": "5a25a077-e0d2-41a6-ac4f-48b96537ce3c", "name": "CODDEVOL", "value": "337", "type": "string"}, {"id": "83a3edce-648c-44d8-864e-722902f64bd8", "name": "CODATV1", "value": "1", "type": "string"}, {"id": "b163cce6-a7eb-4d4d-997f-943da7b84c51", "name": "CODUSURDEVOL", "value": 68, "type": "string"}, {"id": "baab24ae-9778-41f8-a3c3-7bcb002bc6cc", "name": "CODFUNC", "value": 1, "type": "number"}, {"id": "67c5933a-e817-4055-baa2-e866fd2189cd", "name": "CODBANCO", "value": 7777, "type": "number"}]}, "includeOtherFields": true, "options": {}}, "id": "3445cb41-b3af-4c59-a8f7-104e026eddd5", "name": "Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [-2920, 1600]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2160, 1480], "id": "b90966b8-20fd-4bb7-ae4c-38353838b31f", "name": "If"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2540, 1460], "id": "7b9f445c-fbee-4512-98d7-f1a3d0fe8e0a", "name": "Loop"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "727fe704-a753-46ac-8b6e-16b18f64a81a", "name": "Entrada/Saida", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2760, 1600]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "fed30036-0d98-4f14-85f4-504aa8c999b3", "leftValue": "={{ $json.not_found }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-2160, 1760], "id": "dd339864-845f-46d5-a65c-df630f285120", "name": "If1"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2540, 1740], "id": "b2708be3-6bf4-41f5-bd0d-39e516479360", "name": "Loop1"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSVENDA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND EXISTS (SELECT 1\n        FROM PCMOV\n        WHERE NUMTRANSVENDA = N.NUMTRANSVENDA\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2340, 1480], "id": "acdb1372-c0c4-4aa0-aa5d-0e0a5acc9424", "name": "Validar NF Saida", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "MIMOTESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT N.NUMTRANSENT\n  FROM PCNFENT N\n WHERE CHAVENFE = '{{ $json.chNFe }}'\nAND EXISTS (SELECT NUMTRANSENT\n        FROM PCMOV\n        WHERE NUMTRANSENT = N.NUMTRANSENT\n          AND ROWNUM = 1)", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-2340, 1760], "id": "ad9705ea-5061-4156-9375-d4b5068b8bd5", "name": "Validar NF Entrada", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "MIMOTESTE"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n    RETORNO NUMBER := {{ $json.NUMTRANSVENDA }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.VENDAS_SAIDA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1960, 1500], "id": "95f1a5bc-5ba0-49d1-897a-e26937a187d4", "name": "Baixa Estoque", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "MIMOTESTE"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "DECLARE\n    RETORNO NUMBER := {{ $json.NUMTRANSENT }};\n    vnERRO EXCEPTION;\n    vsMSGRETORNO VARCHAR2(1000) := 'OK';\nBEGIN\n    RETORNO := PKG_ESTOQUE.COMPRAS_ENTRADA(RETORNO, 'N', vsMSGRETORNO);\n\n    IF NVL(vsMSGRETORNO, 'OK') <> 'OK' THEN\n        RAISE vnERRO;\n    END IF;\n\n    IF RETORNO <= 0 THEN\n        vsMSGRETORNO := 'Não houve movimentação de estoque para a transação informada';\n        RAISE vnERRO;\n    END IF;\n\nEXCEPTION\n    WHEN vnERRO THEN\n        RAISE_APPLICATION_ERROR(-20099, vsMSGRETORNO);\n    WHEN OTHERS THEN\n        RAISE_APPLICATION_ERROR(-20010, 'Erro ao executar movimentação de estoque.' || CHR(13) || SQLERRM);\nEND;", "options": {}}, "type": "CUSTOM.oracleSql", "typeVersion": 2.4, "position": [-1960, 1780], "id": "cd3e8e8a-6d8a-4443-9aa0-b2cc80bb4003", "name": "Alimenta Estoque", "credentials": {"oracleSql": {"id": "yYYtYqcAzeUOC9IT", "name": "MIMOTESTE"}}, "onError": "continueErrorOutput"}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1760, 1520], "id": "9a4b935a-556b-4a91-93a4-f784a9814358", "name": "Notificar <PERSON>", "webhookId": "e5eb2f04-fb71-4e8a-8fe7-47a08eac76a1", "credentials": {"telegramApi": {"id": "H7oj3rfPQ0v3Imkw", "name": "NERVSFLOW - bot"}}}, {"parameters": {"chatId": "-1002342190385", "text": "=💢 *Workflow:* {{ $workflow.name }} \n📌 {{ $json.error }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-1760, 1800], "id": "a0e3089b-4948-4f5b-802a-52c242488a0f", "name": "Notificar Error1", "webhookId": "e5eb2f04-fb71-4e8a-8fe7-47a08eac76a1", "credentials": {"telegramApi": {"id": "H7oj3rfPQ0v3Imkw", "name": "NERVSFLOW - bot"}}}, {"parameters": {"fileSelector": "//192.168.1.245/NERVSFLOW/TESTE/*.xml", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [-3720, 1600], "id": "c9750076-1764-4b6c-912d-5b8d302fc2be", "name": "Ler XML"}, {"parameters": {"operation": "xml", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [-3560, 1600], "id": "adb30170-f479-4d33-9f7f-a0ae8b54a169", "name": "Extract from File"}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Variables", "type": "main", "index": 0}]]}, "Variables": {"main": [[{"node": "Entrada/Saida", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Baixa Estoque", "type": "main", "index": 0}]]}, "Loop": {"main": [[], [{"node": "Validar NF Saida", "type": "main", "index": 0}]]}, "Entrada/Saida": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Loop1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}], [{"node": "Alimenta Estoque", "type": "main", "index": 0}]]}, "Loop1": {"main": [[], [{"node": "Validar NF Entrada", "type": "main", "index": 0}]]}, "Validar NF Saida": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Validar NF Entrada": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "Baixa Estoque": {"main": [[{"node": "Loop", "type": "main", "index": 0}], [{"node": "Notificar <PERSON>", "type": "main", "index": 0}]]}, "Alimenta Estoque": {"main": [[{"node": "Loop1", "type": "main", "index": 0}], [{"node": "Notificar Error1", "type": "main", "index": 0}]]}, "Notificar Error": {"main": [[{"node": "Loop", "type": "main", "index": 0}]]}, "Notificar Error1": {"main": [[{"node": "Loop1", "type": "main", "index": 0}]]}, "Ler XML": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Extract from File": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "591676539c4f0e99a31b25b703974a0d3a8a14d1f2b0953b9da4e2c55d261ce1"}}