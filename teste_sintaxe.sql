-- Teste de sintaxe da procedure
SET SERVEROUTPUT ON SIZE 1000000;

-- Verificar se a procedure compila
PROMPT Compilando procedure nervsflow_integradora...

-- Executar o arquivo da procedure
@nervsflow_integradora.sql

-- Verificar se houve erros de compilação
PROMPT Verificando erros de compilação...

SELECT line, position, text 
FROM user_errors 
WHERE name = 'NERVSFLOW_INTEGRADORA' 
AND type = 'PROCEDURE'
ORDER BY line, position;

-- Verificar se a procedure foi criada
PROMPT Verificando se a procedure foi criada...

SELECT object_name, object_type, status, last_ddl_time
FROM user_objects 
WHERE object_name = 'NERVSFLOW_INTEGRADORA';

PROMPT Teste de sintaxe concluído.
