-- =====================================================
-- TESTE MANUAL DA PROCEDURE NERVSFLOW_INTEGRADORA
-- Arquivo separado para testes de desenvolvimento
-- =====================================================

-- Habilitar DBMS_OUTPUT para ver os logs de debug
   SET SERVEROUTPUT ON SIZE 1000000;

-- =====================================================
-- VERIFICAÇÃO E RECOMPILAÇÃO DA PROCEDURE
-- =====================================================

-- Verificar status da procedure
select object_name,
       object_type,
       status,
       created,
       last_ddl_time
  from user_objects
 where object_name = 'NERVSFLOW_INTEGRADORA';

-- Verificar erros de compilação (se houver)
select line,
       position,
       text as erro
  from user_errors
 where name = 'NERVSFLOW_INTEGRADORA'
   and type = 'PROCEDURE'
 order by line,
          position;

-- Verificar estrutura das tabelas principais
PROMPT =====================================================
PROMPT ESTRUTURA DAS TABELAS:
PROMPT =====================================================

-- PCMOV
select 'PCMOV' as tabela,
       column_name,
       data_type,
       data_length
  from user_tab_columns
 where table_name = 'PCMOV'
   and column_name in ( 'NUMTRANSVENDA',
                        'NUMTRANSITEM',
                        'CODFILIAL',
                        'CODCLI',
                        'CODPROD',
                        'QT',
                        'PUNIT',
                        'DTMOV' )
 order by column_id;

-- PCMOVCOMPLE
select 'PCMOVCOMPLE' as tabela,
       column_name,
       data_type,
       data_length
  from user_tab_columns
 where table_name = 'PCMOVCOMPLE'
   and column_name in ( 'NUMTRANSITEM',
                        'CODPROD' )
 order by column_id;

-- PCNFSAID
select 'PCNFSAID' as tabela,
       column_name,
       data_type,
       data_length
  from user_tab_columns
 where table_name = 'PCNFSAID'
   and column_name in ( 'NUMTRANSVENDA',
                        'CODFILIAL',
                        'CODCLI',
                        'CHAVENFE',
                        'SERIE',
                        'NUMNF',
                        'NUMNOTA',
                        'DTSAIDA',
                        'VLTOTAL' )
 order by column_id;

-- Se houver erros, recompilar a procedure
-- ALTER PROCEDURE NERVSFLOW_INTEGRADORA COMPILE;

PROMPT
PROMPT =====================================================
PROMPT SE A PROCEDURE ESTIVER INVALID:
PROMPT 1. Execute: @nervsflow_integradora.sql
PROMPT 2. Verifique os erros acima
PROMPT 3. Execute este teste novamente
PROMPT =====================================================
PROMPT

declare
   v_retorno    varchar2(4000);
   v_dados_json clob;
begin
  -- JSON com dados reais da NFe (baseado no exemplo fornecido)
   v_dados_json := '[{
    "chavenfe": "31250105330305000509550010000005801205371625",
    "serie": "1",
    "numnota": 580,
    "tpnf": 1,
    "finnfe": 1,
    "tpimp": 1,
    "modelo": "55",
    "natop": "Revenda Nao Contribuinte",
    "dtemissao": "01/01/2025 16:12:23",
    "cstat": "100",
    "nprot": "131256393522498",
    "cnpjemitente": "05330305000509",
    "ufemitente": "MG",
    "cnpjdestinatario": "27220749821",
    "ufdestinatario": "SP",
    "refnfe": "",
    "tipofj": "F",
    "codcob": "D2C",
    "infcpl": "Total aproximado de tributos",
    "xlgr": "Rua Doutor Abelardo Vergueiro Cesar",
    "nro": "45",
    "xbairro": "Vila Alexandria",
    "xmun": "Sao Paulo",
    "xpais": "Brasil",
    "cpais": "1058",
    "cmun": "3550308",
    "cep": "04635080",
    "indfinal": "1",
    "indiedest": "9",
    "vltotal": 331.87,
    "vprod": 353.96,
    "vicms": 13.28,
    "vbc": 331.87,
    "vpis": 5.27,
    "vcofins": 24.22,
    "vipi": 0,
    "vfrete": 11.29,
    "voutros": 0,
    "vdesc": 33.38,
    "nitem": 1,
    "cprod": "10246",
    "cean": "7908216132733",
    "xprod": "Faca para Legumes e Frutas",
    "ncm": "82119100",
    "cfop": 6108,
    "ucom": "un",
    "qcom": 1,
    "vuncom": 87.99,
    "vprod_item": 87.99,
    "vdesc_item": 8.3,
    "vfrete_item": 2.81,
    "voutro": 0,
    "orig": "1",
    "cst": "00",
    "picms": 4,
    "vbc_item": 82.5,
    "vicms_item": 3.3,
    "vbcstret": 0,
    "vicmsstret": 0,
    "pst": 0,
    "vicmssubstituto": 0,
    "vbcst": 0,
    "picmsst": 0,
    "pmvast": 0,
    "vicmsst": 0,
    "ppis": 1.65,
    "vbcpis": 79.2,
    "vpis_item": 1.31,
    "pcofins": 7.6,
    "vbccofins": 79.2,
    "vcofins_item": 6.02,
    "cst_pis_cofins": "01",
    "pipi": 0,
    "vbcipi": 0,
    "vipi_item": 0,
    "vipidev": 0,
    "codsittribipi": "00",
    "vbcufdest": 82.5,
    "picmsufdest": 18,
    "picmsinter": 4,
    "picmsinterpart": 100,
    "vicmsufdest": 11.55,
    "vicmsufrem": 0,
    "pfcpufdest": 0,
    "vfcpufdest": 0,
    "punitcont": 79.69,
    "vlbasepiscofins": 84.69,
    "vlbaseipi": 87.99,
    "vfreiteitem": 2.81,
    "vdescitem": 8.3,
    "vipiitem": 0,
    "baseicms": 82.5,
    "baseicst": 0,
    "st": 0,
    "stbcr": 0,
    "vlicmsbcr": 0,
    "vlicmspartdest": 11.55,
    "vlicmspartrem": 0,
    "vlfcppart": 0,
    "codcest": "",
    "xped": "",
    "infadprod": "Cor: Prata",
    "is_devolucao": "N",
    "vbc_pis": 79.2,
    "vbc_cofins": 79.2
  }]';
   dbms_output.put_line('🧪 TESTE MANUAL COM DADOS REAIS - DEBUG SQL');

  -- Primeiro verificar se a procedure existe e está válida
   for rec in (
      select object_name,
             status,
             last_ddl_time
        from user_objects
       where object_name = 'NERVSFLOW_INTEGRADORA'
   ) loop
      dbms_output.put_line('? Procedure: '
                           || rec.object_name
                           || ' - Status: '
                           || rec.status);
      if rec.status != 'VALID' then
         dbms_output.put_line('❌ ERRO: Procedure está INVALID - precisa recompilar!');
         dbms_output.put_line('🔧 Execute primeiro: @nervsflow_integradora.sql');
         return;
      end if;
   end loop;

   dbms_output.put_line('? Iniciando processamento da NFe...');
   dbms_output.put_line('📊 Tamanho do JSON: '
                        || length(v_dados_json)
                        || ' caracteres');

  -- Chamar a procedure
   nervsflow_integradora(
      v_dados_json,
      v_retorno
   );
  
  -- Exibir resultado
   dbms_output.put_line('📤 Resultado: ' || v_retorno);
  
  -- Verificar se houve sucesso
   if v_retorno like 'SUCCESS%' then
      commit;
      dbms_output.put_line('✅ NFe processada com sucesso!');
    
    -- Verificar dados gravados
      for rec in (
         select count(*) as total
           from pcmov
          where trunc(dtmov) = trunc(sysdate)
      ) loop
         dbms_output.put_line('📦 Total de itens em PCMOV hoje: ' || rec.total);
      end loop;
    
    -- Verificar dados na PCNFSAID
      for rec in (
         select count(*) as total
           from pcnfsaid
          where trunc(dtsaida) = trunc(sysdate)
            and dtcancel is null
      ) loop
         dbms_output.put_line('📋 Total de notas em PCNFSAID hoje: ' || rec.total);
      end loop;

   else
      rollback;
      dbms_output.put_line('❌ Erro na procedure: ' || v_retorno);
   end if;

exception
   when others then
      dbms_output.put_line('💥 ERRO GERAL: ' || sqlerrm);
      rollback;
end;
/

-- =====================================================
-- VERIFICAÇÕES ADICIONAIS APÓS O TESTE
-- =====================================================

PROMPT
PROMPT =====================================================
PROMPT VERIFICAÇÕES PÓS-TESTE:
PROMPT =====================================================

-- Verificar se a procedure existe e está válida
select object_name,
       object_type,
       status,
       last_ddl_time
  from user_objects
 where object_name = 'NERVSFLOW_INTEGRADORA';

-- Verificar últimas transações
select proxnumtransvenda,
       proxnumtransent
  from pcconsum;

-- Verificar se há dados processados hoje
select 'PCMOV' as tabela,
       count(*) as registros
  from pcmov
 where trunc(dtmov) = trunc(sysdate)
union all
select 'PCNFSAID' as tabela,
       count(*) as registros
  from pcnfsaid
 where trunc(dtsaida) = trunc(sysdate)
   and dtcancel is null
union all
select 'PCNFENT' as tabela,
       count(*) as registros
  from pcnfent
 where trunc(dtent) = trunc(sysdate)
   and dtcancel is null;

PROMPT
PROMPT =====================================================
PROMPT INSTRUÇÕES:
PROMPT =====================================================
PROMPT 1. Execute este arquivo para testar a procedure
PROMPT 2. Analise os logs de debug no SCRIPT OUTPUT
PROMPT 3. Verifique se os dados foram gravados nas tabelas
PROMPT 4. Em caso de erro, analise o SQL gerado pela DYNAMIC_INSERT
PROMPT =====================================================