# Controle Transacional - Procedure NERVSFLOW_INTEGRADORA

## 📋 Resumo das Melhorias Implementadas

A procedure `nervsflow_integradora` foi atualizada para implementar **controle transacional robusto** e corrigir a geração de números de transação para notas de entrada.

## 🔧 Principais Correções

### 1. **Controle Transacional Completo**
- ✅ **Savepoint no início**: `savepoint inicio_processamento`
- ✅ **Rollback em qualquer erro**: Todas as operações são desfeitas se houver falha
- ✅ **Commit apenas no final**: Após todos os inserts serem bem-sucedidos
- ✅ **Exception handling**: Cada bloco de insert tem tratamento de erro individual

### 2. **Geração de Números de Transação Corrigida**
- ✅ **NUMTRANSVENDA**: Para notas de saída (tpNF = '1')
- ✅ **NUMTRANSENT**: Para notas de entrada (tpNF = '0') - **CORRIGIDO**
- ✅ **Obtidos no início**: Ambos os números são obtidos logo no começo
- ✅ **Atualizados no final**: Sequências são incrementadas apenas após sucesso

### 3. **Estrutura de Controle de Erro**

```sql
-- Início da procedure
savepoint inicio_processamento;

-- Obter números de sequência
BEGIN
   SELECT proxnumtransvenda INTO v_numtransvenda FROM pcconsum FOR UPDATE;
   SELECT proxnumtransent INTO v_numtransent FROM pcconsum FOR UPDATE;
EXCEPTION
   WHEN OTHERS THEN
      rollback to inicio_processamento;
      raise_application_error(-20001, 'Erro ao obter sequências');
END;

-- Cada INSERT tem tratamento individual
BEGIN
   INSERT INTO tabela...
EXCEPTION
   WHEN OTHERS THEN
      rollback to inicio_processamento;
      raise_application_error(-20001, 'Erro específico');
END;

-- Final: atualizar sequências e commit
BEGIN
   UPDATE pcconsum SET proxnumtransvenda/proxnumtransent...
   COMMIT;
EXCEPTION
   WHEN OTHERS THEN
      rollback to inicio_processamento;
      raise_application_error(-20001, 'Erro na finalização');
END;
```

## 🎯 Benefícios da Implementação

### **Consistência de Dados**
- ❌ **Antes**: Se desse erro no meio, dados ficavam inconsistentes
- ✅ **Agora**: Tudo ou nada - dados sempre consistentes

### **Controle de Sequências**
- ❌ **Antes**: Só tinha controle para NUMTRANSVENDA
- ✅ **Agora**: Controle completo para NUMTRANSVENDA e NUMTRANSENT

### **Rastreabilidade de Erros**
- ❌ **Antes**: Erros genéricos, difícil identificar origem
- ✅ **Agora**: Mensagens específicas por operação

### **Recuperação de Falhas**
- ❌ **Antes**: Rollback parcial, dados órfãos
- ✅ **Agora**: Rollback completo, estado limpo

## 📊 Fluxo de Processamento

```mermaid
graph TD
    A[Início] --> B[Savepoint]
    B --> C[Obter NUMTRANSVENDA]
    C --> D[Obter NUMTRANSENT]
    D --> E[Validar NFe Duplicada]
    E --> F[Loop Itens]
    F --> G[INSERT PCMOV]
    G --> H[INSERT PCMOVCOMPLE]
    H --> I[Próximo Item?]
    I -->|Sim| F
    I -->|Não| J[INSERT PCNFSAID]
    J --> K[INSERT PCNFBASE]
    K --> L[INSERT PCPREST]
    L --> M[Atualizar Sequências]
    M --> N[COMMIT]
    N --> O[Sucesso]
    
    G -->|Erro| P[Rollback]
    H -->|Erro| P
    J -->|Erro| P
    K -->|Erro| P
    L -->|Erro| P
    M -->|Erro| P
    P --> Q[Erro]
```

## 🔍 Pontos de Controle

### **Validações Iniciais**
1. Obtenção de números de sequência
2. Validação de NFe duplicada
3. Busca de filial e cliente

### **Processamento de Itens**
1. INSERT em PCMOV com controle de erro
2. INSERT em PCMOVCOMPLE com controle de erro
3. Cálculo de CMV e custos

### **Finalização**
1. INSERT em PCNFSAID
2. INSERT em PCNFBASE  
3. INSERT em PCPREST
4. Atualização das sequências
5. COMMIT final

## ⚠️ Importante

- **Todas as operações** são executadas em uma única transação
- **Qualquer erro** desfaz todas as operações realizadas
- **Números de sequência** são reservados no início e liberados apenas no sucesso
- **Mensagens de erro** são específicas para facilitar debugging

## 🧪 Teste da Implementação

Para testar o controle transacional:

```sql
-- Execute a procedure com dados válidos
@teste_manual_procedure.sql

-- Verifique se os dados foram inseridos corretamente
SELECT COUNT(*) FROM pcmov WHERE trunc(dtmov) = trunc(sysdate);
SELECT proxnumtransvenda, proxnumtransent FROM pcconsum;
```

## 📝 Logs de Debug

A procedure agora inclui logs detalhados:
- ✅ Números de transação obtidos
- ✅ Tipo de nota processada (Entrada/Saída)
- ✅ Chave da NFe processada
- ✅ Status final do processamento
