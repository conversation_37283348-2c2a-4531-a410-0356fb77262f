# 📊 MAPEAMENTO COMPLETO DE CAMPOS XML → ORACLE

## 🔍 Baseado na Análise dos Arquivos N8N

### 📋 Tabela PCMOV (Movimentação de Produtos)

**Baseado no arquivo `5-GRAVAR-ITENS-SAIDA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSVENDA** | Sequence Oracle | NUMBER | `v_numtransvenda` | PCCONSUM.PROXNUMTRANSVENDA |
| **NUMTRANSITEM** | Sequence Oracle | NUMBER | `v_numtransitem` | DFSEQ_PCMOVCOMPLE.NEXTVAL |
| **NUMSEQ** | det/nItem | NUMBER | `nitem` | Número sequencial do item |
| **CODOPER** | Calculado | VARCHAR2 | `v_codoper` | S/SR/ED/ER baseado em tpNF+natOp |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota fiscal |
| **VLOUTRASDESP** | det/prod/vOutro | NUMBER | `voutro` | Outras despesas do item |
| **DTMOVLOG** | ide/dhEmi | DATE | `TO_DATE(dtemissao, 'DD/MM/YYYY HH24:MI:SS')` | Data/hora completa |
| **DTMOV** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Apenas data |
| **CODSITTRIBPISCOFINS** | PIS/COFINS CST | VARCHAR2 | `cst_pis_cofins` | CST PIS/COFINS |
| **CODPLPAG** | Fixo | NUMBER | `1` | Plano de pagamento |
| **NUMCAR** | Fixo | NUMBER | `1` | Número do carregamento |
| **CODCLI** | dest/CNPJ | NUMBER | `v_codcli` | Buscar na PCCLIENT |
| **CODFILIAL** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Buscar na PCFILIAL |
| **CODFILIALNF** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Mesmo que CODFILIAL |
| **STATUS** | Fixo | VARCHAR2 | `'AB'` | Status da movimentação |
| **NUMTRANSENT** | NULL | NUMBER | `NULL` | Para notas de entrada |
| **CODFORNEC** | NULL | NUMBER | `NULL` | Para notas de saída |
| **PERCDESC** | Fixo | NUMBER | `0` | Percentual desconto |
| **VLBONIFIC** | Fixo | NUMBER | `0` | Valor bonificação |
| **CODFILIALRETIRA** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Filial de retirada |
| **GERAICMSLIVROFISCAL** | Fixo | VARCHAR2 | `'S'` | Gera ICMS livro fiscal |
| **COMPRACONSIGNADO** | Fixo | VARCHAR2 | `'N'` | Compra consignado |
| **MOVESTOQUECONTABIL** | Fixo | VARCHAR2 | `'S'` | Move estoque contábil |
| **MOVESTOQUEGERENCIAL** | Fixo | VARCHAR2 | `'S'` | Move estoque gerencial |
| **CODPROD** | det/prod/cProd | NUMBER | `cprod` | Código do produto |
| **CODAUXILIAR** | det/prod/cEAN | NUMBER | `NVL(cean, 0)` | Código auxiliar (EAN) |
| **NBM** | det/prod/NCM | VARCHAR2 | `ncm` | Nomenclatura NBM |
| **CODFISCAL** | det/prod/CFOP | NUMBER | `cfop` | Código fiscal CFOP |
| **UNIDADE** | det/prod/uCom | VARCHAR2 | `ucom` | Unidade de medida |
| **QT** | det/prod/qCom | NUMBER | `qcom` | Quantidade |
| **QTCONT** | det/prod/qCom | NUMBER | `qcom` | Quantidade contábil |
| **PUNIT** | det/prod/vUnCom | NUMBER | `vuncom` | Preço unitário |
| **PUNITCONT** | Calculado | NUMBER | `punitcont` | Preço unitário contábil |
| **DESCRICAO** | Consulta PCPRODUT | VARCHAR2 | `(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = cprod)` | Descrição do produto |
| **VLDESCONTO** | Calculado | NUMBER | `vdesc_item` | Valor desconto item |
| **PERCICM** | det/imposto/ICMS/pICMS | NUMBER | `picms` | Percentual ICMS |
| **SITTRIBUT** | det/imposto/ICMS/CST | VARCHAR2 | `cst` | Situação tributária |
| **VLFRETE** | Calculado | NUMBER | `vfrete_item` | Valor frete item |
| **BASEICMS** | Calculado | NUMBER | `baseicms` | Base ICMS |
| **BASEICMSBCR** | Fixo | NUMBER | `0` | Base ICMS BCR |
| **BASEBCR** | Fixo | NUMBER | `0` | Base BCR |
| **STBCR** | Calculado | NUMBER | `stbcr` | ST BCR |
| **VLICMSBCR** | Calculado | NUMBER | `vlicmsbcr` | Valor ICMS BCR |
| **BASEICST** | Calculado | NUMBER | `baseicst` | Base ICMS ST |
| **ST** | Calculado | NUMBER | `st` | Valor ICMS ST |
| **PERCST** | Fixo | NUMBER | `0` | Percentual ST |
| **IVA** | Fixo | NUMBER | `0` | IVA |
| **PERPIS** | det/imposto/PIS/pPIS | NUMBER | `ppis` | Percentual PIS |
| **VLPIS** | Calculado | NUMBER | `vpis_item` | Valor PIS |
| **VLBASEPISCOFINS** | Calculado | NUMBER | `vlbasepiscofins` | Base PIS/COFINS |
| **PERCOFINS** | det/imposto/COFINS/pCOFINS | NUMBER | `pcofins` | Percentual COFINS |
| **VLCOFINS** | Calculado | NUMBER | `vcofins_item` | Valor COFINS |
| **VLIPI** | Calculado | NUMBER | `vipi_item` | Valor IPI |
| **PERCIPI** | det/imposto/IPI/pIPI | NUMBER | `pipi` | Percentual IPI |
| **VLBASEIPI** | Calculado | NUMBER | `vlbaseipi` | Base IPI |
| **NUMLOTE** | NULL | VARCHAR2 | `NULL` | Número do lote |
| **CODICMTAB** | Consulta CUSTOS | NUMBER | `0` | Código ICMS tabela |
| **CODST** | Consulta CUSTOS | NUMBER | `0` | Código ST |
| **PTABELA** | det/prod/vUnCom | NUMBER | `vuncom` | Preço tabela |
| **CUSTOFIN** | Calculado CMV | NUMBER | `custofin` | Custo financeiro |
| **CUSTOREAL** | Calculado CMV | NUMBER | `custoreal` | Custo real |
| **CUSTOREP** | Fixo | NUMBER | `0` | Custo reposição |
| **CUSTOCONT** | Fixo | NUMBER | `0` | Custo contábil |
| **CUSTOFINEST** | Fixo | NUMBER | `0` | Custo financeiro estoque |
| **CODUSUR** | Variável N8N | NUMBER | `v_codrca` | Código usuário |
| **NUMPED** | det/prod/xPed | NUMBER | `NVL(xped, 0)` | Número pedido |
| **NUMREGIAO** | Fixo | NUMBER | `1` | Número região |
| **CODEPTO** | Consulta PCPRODUT | NUMBER | `(SELECT CODEPTO FROM PCPRODUT WHERE CODPROD = cprod)` | Código departamento |
| **CODSEC** | Consulta PCPRODUT | NUMBER | `(SELECT CODSEC FROM PCPRODUT WHERE CODPROD = cprod)` | Código seção |

### 📋 Tabela PCMOVCOMPLE (Complemento da Movimentação)

**Baseado no arquivo `5-GRAVAR-ITENS-SAIDA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSITEM** | Sequence Oracle | NUMBER | `v_numtransitem` | DFSEQ_PCMOVCOMPLE.NEXTVAL |
| **DTREGISTRO** | ide/dhEmi | DATE | `TO_DATE(dtemissao, 'DD/MM/YYYY HH24:MI:SS')` | Data/hora registro |
| **ALIQICMS1RET** | det/imposto/ICMS/pST | NUMBER | `pst` | Alíquota ICMS retenção |
| **VLBASEPARTDEST** | det/imposto/ICMSUFDest/vBCUFDest | NUMBER | `vbcufdest` | Base partilha destino |
| **ALIQINTERNADEST** | det/imposto/ICMSUFDest/pICMSUFDest | NUMBER | `picmsufdest` | Alíquota interna destino |
| **ALIQINTERORIGPART** | det/imposto/ICMSUFDest/pICMSInter | NUMBER | `picmsinter` | Alíquota inter origem |
| **PERCPROVPART** | det/imposto/ICMSUFDest/pICMSInterPart | NUMBER | `picmsinterpart` | Percentual provisório |
| **VLICMSPARTDEST** | Calculado | NUMBER | `vlicmspartdest` | Valor ICMS partilha destino |
| **VLICMSPARTREM** | Calculado | NUMBER | `vlicmspartrem` | Valor ICMS partilha remetente |
| **ALIQFCP** | det/imposto/ICMSUFDest/pFCPUFDest | NUMBER | `pfcpufdest` | Alíquota FCP |
| **VLFCPPART** | Calculado | NUMBER | `vlfcppart` | Valor FCP partilha |
| **NUMCHAVEEXP** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave NFe |
| **CODPRODGNRE** | det/prod/cProd | NUMBER | `cprod` | Código produto GNRE |
| **EANCODPROD** | det/prod/cEAN | NUMBER | `NVL(cean, 0)` | EAN código produto |
| **CODSITTRIBIPI** | NULL | VARCHAR2 | `NULL` | Situação tributária IPI |
| **CODTRIBPISCOFINS** | Fixo | NUMBER | `7` | Código tributação PIS/COFINS |
| **NITEMXML** | det/nItem | NUMBER | `nitem` | Número item XML |
| **CODCEST** | det/prod/CEST | VARCHAR2 | `codcest` | Código CEST |
| **ORIGMERCTRIB** | Fixo | NUMBER | `1` | Origem mercadoria tributação |
| **VLBASEFRETE** | Fixo | NUMBER | `0` | Base frete (0 para não duplicar) |
| **VLBASEOUTROS** | Fixo | NUMBER | `0` | Base outros (0 para não duplicar) |

### 📋 Tabela PCNFSAID (Notas Fiscais de Saída)

**Baseado no arquivo `4-GRAVAR-NF-SAIDA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSVENDA** | Sequence Oracle | NUMBER | `v_numtransvenda` | PCCONSUM.PROXNUMTRANSVENDA |
| **CODFILIAL** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Buscar na PCFILIAL |
| **CODCLI** | dest/CNPJ | NUMBER | `v_codcli` | Buscar na PCCLIENT |
| **CHAVENFE** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave da NFe |
| **SERIE** | ide/serie | VARCHAR2 | `serie` | Série da nota |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota |
| **DTSAIDA** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data de saída |
| **VLTOTAL** | total/ICMSTot/vNF | NUMBER | `vltotal` | Valor total da nota |
| **VLCUSTOFIN** | Soma CUSTOFIN*QT | NUMBER | `Soma dos custos financeiros` | Valor custo financeiro total |
| **VLCUSTOREAL** | Soma CUSTOREAL*QT | NUMBER | `Soma dos custos reais` | Valor custo real total |
| **VLCUSTOREP** | Soma CUSTOREP*QT | NUMBER | `Soma dos custos reposição` | Valor custo reposição total |
| **VLCUSTOCONT** | Soma CUSTOCONT*QT | NUMBER | `Soma dos custos contábeis` | Valor custo contábil total |

### 📋 Tabela PCNFENT (Notas Fiscais de Entrada)

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSENT** | Sequence Oracle | NUMBER | `v_numtransent` | PCCONSUM.PROXNUMTRANSENT |
| **CODFILIAL** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Buscar na PCFILIAL |
| **CODFORNEC** | dest/CNPJ | NUMBER | `v_codfornec` | Buscar na PCCLIENT |
| **CHAVENFE** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave da NFe |
| **SERIE** | ide/serie | VARCHAR2 | `serie` | Série da nota |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota |
| **DTENT** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data de entrada |
| **VLTOTAL** | total/ICMSTot/vNF | NUMBER | `vltotal` | Valor total da nota |

### 📋 Tabela PCNFBASE (Base das Notas Fiscais)

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **CHAVENFE** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave da NFe |
| **SERIE** | ide/serie | VARCHAR2 | `serie` | Série da nota |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota |

### 📋 Tabela PCPREST (Prestações)

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSVENDA** | Sequence Oracle | NUMBER | `v_numtransvenda` | PCCONSUM.PROXNUMTRANSVENDA |
| **CODCLI** | dest/CNPJ | NUMBER | `v_codcli` | Buscar na PCCLIENT |
| **VALOR** | total/ICMSTot/vNF | NUMBER | `vltotal` | Valor da prestação |
| **DTVENC** | ide/dhEmi + 30 dias | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY') + 30` | Data vencimento |

### 📋 Tabela PCPRESTOBS (Observações das Prestações)

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSVENDA** | Sequence Oracle | NUMBER | `v_numtransvenda` | PCCONSUM.PROXNUMTRANSVENDA |
| **CODCLI** | dest/CNPJ | NUMBER | `v_codcli` | Buscar na PCCLIENT |

## 🔧 Variáveis N8N Utilizadas

### 📊 Variáveis de Configuração (definidas no N8N)

| Variável N8N | Uso na Procedure | Valor Exemplo | Observações |
|--------------|------------------|---------------|-------------|
| **CODRCA** | `v_codrca` | `802` | Código do RCA |
| **CODPRACA** | `v_codpraca` | `331` | Código da praça |
| **CODSUPERVISOR** | `v_codsupervisor` | `9` | Código do supervisor |
| **CODDEVOL** | `v_coddevol` | `337` | Código devolução |
| **CODATV1** | `v_codatv1` | `1` | Código atividade |
| **CODFUNC** | `v_codfunc` | `1` | Código funcionário |

## ⚠️ CAMPOS CRÍTICOS - NUNCA ALTERAR

### 🔒 Campos Protegidos

1. **CHAVENFE** - Chave única da NFe (44 dígitos)
2. **NUMTRANSVENDA/NUMTRANSENT** - Controle transacional Oracle
3. **PUNITCONT** - Cálculo complexo já validado pelo dados_xml.js
4. **VLBASEPISCOFINS** - Base PIS/COFINS específica calculada
5. **BASEICMS** - Base ICMS com correções aplicadas
6. **DTMOVLOG** - Data/hora original da NFe
7. **CODOPER** - Código operação baseado em lógica tpNF + natOp

### 🧮 Campos Calculados pelo dados_xml.js

Estes campos são calculados pelo script JavaScript e devem ser usados diretamente:

- `punitcont` - Preço unitário contábil
- `vlbasepiscofins` - Base PIS/COFINS
- `baseicms` - Base ICMS unitária
- `vlbaseipi` - Base IPI
- `vfreiteitem` - Frete por item
- `vdescitem` - Desconto por item
- `vipiitem` - IPI por item
- `baseicst` - Base ICMS ST por item
- `st` - ICMS ST por item
- `stbcr` - ICMS ST retido por item
- `vlicmsbcr` - ICMS substituto por item
- `vlicmspartdest` - ICMS partilha destino por item
- `vlicmspartrem` - ICMS partilha remetente por item
- `vlfcppart` - FCP partilha por item
- `custofin` - Custo financeiro calculado
- `custoreal` - Custo real calculado

## 🎯 IMPORTANTE: USE ESTE MAPEAMENTO

Este mapeamento é baseado na análise real dos arquivos N8N `4-GRAVAR-NF-SAIDA.json` e `5-GRAVAR-ITENS-SAIDA.json`. 

## 📋 Tabela PCNFENT (Notas Fiscais de Entrada)

**Baseado no arquivo `4.1-GRAVAR-NF-ENTRADA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSENT** | Sequence Oracle | NUMBER | `v_numtransent` | PCCONSUM.PROXNUMTRANSENT |
| **CODFILIAL** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Buscar na PCFILIAL |
| **CODFORNEC** | dest/CNPJ | NUMBER | `v_codfornec` | Buscar na PCCLIENT |
| **CHAVENFE** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave da NFe |
| **SERIE** | ide/serie | VARCHAR2 | `serie` | Série da nota |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota |
| **DTENT** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data de entrada |
| **DTEMISSAO** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data de emissão |
| **VLTOTAL** | total/ICMSTot/vNF | NUMBER | `vltotal` | Valor total da nota |
| **TIPOEMISSAO** | ide/tpImp | NUMBER | `tpimp` | Tipo de impressão |
| **FINALIDADENFE** | ide/finNFe | NUMBER | `finnfe` | Finalidade da NFe |
| **VLFRETE** | total/ICMSTot/vFrete | NUMBER | `vfrete` | Valor do frete |
| **VLST** | total/ICMSTot/vST | NUMBER | `vst` | Valor ICMS ST |
| **BASEICST** | Calculado | NUMBER | `baseicst` | Base ICMS ST |
| **VLIPI** | total/ICMSTot/vIPI | NUMBER | `vipidevol` | Valor IPI devolução |
| **VLPIS** | total/ICMSTot/vPIS | NUMBER | `vpis` | Valor PIS |
| **VLCOFINS** | total/ICMSTot/vCOFINS | NUMBER | `vcofins` | Valor COFINS |
| **CODPAIS** | dest/enderDest/cPais | NUMBER | `cpais` | Código país |
| **DESCPAIS** | dest/enderDest/xPais | VARCHAR2 | `xpais` | Descrição país |
| **CEP** | dest/enderDest/CEP | VARCHAR2 | `cep` | CEP |
| **UF** | dest/enderDest/UF | VARCHAR2 | `ufdest` | UF destino |
| **MUNICIPIO** | dest/enderDest/xMun | VARCHAR2 | `xmun` | Município |
| **ENDERECO** | dest/enderDest/xLgr | VARCHAR2 | `xlgr` | Endereço |
| **BAIRRO** | dest/enderDest/xBairro | VARCHAR2 | `xbairro` | Bairro |
| **VLTOTGER** | total/ICMSTot/vNF | NUMBER | `vltotal` | Valor total geral |
| **PERBASEREDOUTRASDESP** | Fixo | NUMBER | `0` | Percentual base redução outras despesas |
| **CODFISCALFRETE** | Fixo | NUMBER | `0` | Código fiscal frete |
| **PERCICMFRETE** | Fixo | NUMBER | `0` | Percentual ICMS frete |
| **AMBIENTENFE** | Fixo | VARCHAR2 | `'P'` | Ambiente NFe |
| **CONFERIDO** | Fixo | VARCHAR2 | `'N'` | Conferido |
| **AGREGASTVLMERC** | Fixo | VARCHAR2 | `'N'` | Agrega ST valor mercadoria |
| **GERARBCRNFE** | Fixo | VARCHAR2 | `'S'` | Gerar BCR NFe |
| **DTLANCTO** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data lançamento |
| **VLBASEIPI** | Calculado | NUMBER | `vlbaseipi` | Base IPI |
| **SITUACAONFE** | protNFe/infProt/cStat | NUMBER | `cstat` | Situação NFe |
| **ESPECIE** | Fixo | VARCHAR2 | `'NF'` | Espécie |
| **CODFILIALNF** | emit/CNPJ | VARCHAR2 | `v_codfilial` | Código filial NFe |
| **PROTOCOLONFE** | protNFe/infProt/nProt | VARCHAR2 | `nprot` | Protocolo NFe |
| **CODFISCAL** | Calculado | NUMBER | `132 ou 232` | Código fiscal (132=mesmo UF, 232=outro UF) |
| **CODCONT** | Consulta | NUMBER | `v_codcontfor` | Código contábil fornecedor |
| **TIPODESCARGA** | Fixo | VARCHAR2 | `'6'` | Tipo descarga (6=devolução) |
| **CGC** | dest/CNPJ | VARCHAR2 | `cgcent` | CNPJ destinatário |
| **CODFORNECNF** | dest/CNPJ | NUMBER | `v_codfornec` | Código fornecedor NFe |
| **GERANFDEVCLI** | Fixo | VARCHAR2 | `'S'` | Gera NF devolução cliente |
| **MODELO** | ide/mod | VARCHAR2 | `modelo` | Modelo da nota |
| **CODDEVOL** | Variável N8N | NUMBER | `v_coddevol` | Código devolução |
| **EMISSAOPROPRIA** | Fixo | VARCHAR2 | `'N'` | Emissão própria |
| **CODUSURDEVOL** | Variável N8N | NUMBER | `v_codrca` | Código usuário devolução |
| **NUMVIAS** | Fixo | NUMBER | `1` | Número de vias |
| **ALIQICMOUTRASDESP** | Fixo | NUMBER | `0` | Alíquota ICMS outras despesas |
| **CODFISCALOUTRASDESP** | Fixo | NUMBER | `0` | Código fiscal outras despesas |
| **PERPIS** | Fixo | NUMBER | `0` | Percentual PIS |
| **PERCOFINS** | Fixo | NUMBER | `0` | Percentual COFINS |
| **OBS** | infAdProd | VARCHAR2 | `infadprod` | Observações |
| **OBSNFE** | infCpl | VARCHAR2 | `infcpl` | Observações NFe |
| **DTHORAAUTORIZACAOSEFAZ** | ide/dhEmi | DATE | `TO_DATE(dtemissao, 'DD/MM/YYYY HH24:MI:SS')` | Data/hora autorização SEFAZ |

## 📋 Tabela PCMOV (Movimentação de Produtos - ENTRADA)

**Baseado no arquivo `5.1-GRAVAR-ITENS-ENTRADA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSENT** | Sequence Oracle | NUMBER | `v_numtransent` | PCCONSUM.PROXNUMTRANSENT |
| **NUMTRANSITEM** | Sequence Oracle | NUMBER | `v_numtransitem` | DFSEQ_PCMOVCOMPLE.NEXTVAL |
| **NUMSEQ** | det/nItem | NUMBER | `nitem` | Número sequencial do item |
| **CODOPER** | Calculado | VARCHAR2 | `v_codoper` | ER/ED baseado em tipodescarga |
| **NUMNOTA** | ide/nNF | NUMBER | `numnota` | Número da nota fiscal |
| **VLOUTRASDESP** | det/prod/vOutro | NUMBER | `voutro` | Outras despesas do item |
| **DTMOVLOG** | ide/dhEmi | DATE | `TO_DATE(dtemissao, 'DD/MM/YYYY HH24:MI:SS')` | Data/hora completa |
| **DTMOV** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Apenas data |
| **CODSITTRIBPISCOFINS** | PIS/COFINS CST | VARCHAR2 | `cst_pis_cofins` | CST PIS/COFINS |
| **CODPLPAG** | Fixo | NUMBER | `1` | Plano de pagamento |
| **NUMCAR** | Fixo | NUMBER | `0` | Número do carregamento (0 para entrada) |
| **CODCLI** | emit/CNPJ | NUMBER | `v_codcli` | Buscar na PCCLIENT |
| **CODFILIAL** | dest/CNPJ | VARCHAR2 | `v_codfilial` | Buscar na PCFILIAL |
| **CODFILIALNF** | dest/CNPJ | VARCHAR2 | `v_codfilial` | Mesmo que CODFILIAL |
| **STATUS** | Fixo | VARCHAR2 | `'AB'` | Status da movimentação |
| **NUMTRANSVENDA** | NULL | NUMBER | `NULL` | Para notas de entrada |
| **CODFORNEC** | emit/CNPJ | NUMBER | `v_codfornec` | Fornecedor |
| **PERCDESC** | Fixo | NUMBER | `0` | Percentual desconto |
| **VLBONIFIC** | Fixo | NUMBER | `0` | Valor bonificação |
| **CODFILIALRETIRA** | dest/CNPJ | VARCHAR2 | `v_codfilial` | Filial de retirada |
| **GERAICMSLIVROFISCAL** | Fixo | VARCHAR2 | `'S'` | Gera ICMS livro fiscal |
| **COMPRACONSIGNADO** | Fixo | VARCHAR2 | `'N'` | Compra consignado |
| **MOVESTOQUECONTABIL** | Fixo | VARCHAR2 | `'S'` | Move estoque contábil |
| **MOVESTOQUEGERENCIAL** | Fixo | VARCHAR2 | `'S'` | Move estoque gerencial |
| **CODPROD** | det/prod/cProd | NUMBER | `cprod` | Código do produto |
| **CODAUXILIAR** | det/prod/cEAN | NUMBER | `NVL(cean, 0)` | Código auxiliar (EAN) |
| **NBM** | det/prod/NCM | VARCHAR2 | `ncm` | Nomenclatura NBM |
| **CODFISCAL** | det/prod/CFOP | NUMBER | `cfop` | Código fiscal CFOP |
| **UNIDADE** | det/prod/uCom | VARCHAR2 | `ucom` | Unidade de medida |
| **QT** | det/prod/qCom | NUMBER | `qcom` | Quantidade |
| **QTCONT** | det/prod/qCom | NUMBER | `qcom` | Quantidade contábil |
| **PUNIT** | det/prod/vUnCom | NUMBER | `vuncom` | Preço unitário |
| **PUNITCONT** | Calculado | NUMBER | `punitcont` | Preço unitário contábil |
| **DESCRICAO** | Consulta PCPRODUT | VARCHAR2 | `(SELECT DESCRICAO FROM PCPRODUT WHERE CODPROD = cprod)` | Descrição do produto |
| **VLDESCONTO** | Calculado | NUMBER | `vdesc_item` | Valor desconto item |
| **PERCICM** | det/imposto/ICMS/pICMS | NUMBER | `picms` | Percentual ICMS |
| **SITTRIBUT** | det/imposto/ICMS/CST | VARCHAR2 | `cst` | Situação tributária |
| **VLFRETE** | Calculado | NUMBER | `vfrete_item` | Valor frete item |
| **BASEICMS** | Calculado | NUMBER | `baseicms` | Base ICMS |
| **BASEICMSBCR** | det/prod/vUnCom | NUMBER | `vuncom` | Base ICMS BCR |
| **BASEBCR** | Calculado | NUMBER | `vbc_icms` | Base BCR |
| **STBCR** | Calculado | NUMBER | `stbcr` | ST BCR |
| **VLICMSBCR** | Calculado | NUMBER | `vlicmsbcr` | Valor ICMS BCR |
| **BASEICST** | Calculado | NUMBER | `baseicst` | Base ICMS ST |
| **ST** | Calculado | NUMBER | `st` | Valor ICMS ST |
| **PERCST** | Calculado | NUMBER | `percst` | Percentual ST |
| **IVA** | Calculado | NUMBER | `iva` | IVA |
| **PERPIS** | det/imposto/PIS/pPIS | NUMBER | `ppis` | Percentual PIS |
| **VLCREDPIS** | Calculado | NUMBER | `vpis` | Valor crédito PIS |
| **VLBASEPISCOFINS** | Calculado | NUMBER | `vbc_pis` | Base PIS/COFINS |
| **PERCOFINS** | det/imposto/COFINS/pCOFINS | NUMBER | `pcofins` | Percentual COFINS |
| **VLCREDCOFINS** | Calculado | NUMBER | `vcofins` | Valor crédito COFINS |
| **VLIPI** | Calculado | NUMBER | `vipi_item` | Valor IPI |
| **PERCIPI** | det/imposto/IPI/pIPI | NUMBER | `pipi` | Percentual IPI |
| **VLBASEIPI** | Calculado | NUMBER | `vlbaseipi` | Base IPI |
| **NUMLOTE** | Fixo | NUMBER | `1` | Número do lote |
| **CODICMTAB** | Consulta CUSTOS | NUMBER | `v_codicmtab` | Código ICMS tabela |
| **CODST** | Consulta CUSTOS | NUMBER | `v_codst` | Código ST |
| **CUSTOFIN** | Consulta CUSTOS | NUMBER | `v_custofin` | Custo financeiro |
| **CUSTOREAL** | Consulta CUSTOS | NUMBER | `v_custoreal` | Custo real |
| **CUSTOREP** | Consulta CUSTOS | NUMBER | `v_custorep` | Custo reposição |
| **CUSTOCONT** | Consulta CUSTOS | NUMBER | `v_custocont` | Custo contábil |
| **CUSTOFINEST** | Consulta CUSTOS | NUMBER | `v_custofin` | Custo financeiro estoque |
| **PTABELA** | det/prod/vUnCom | NUMBER | `vuncom` | Preço tabela |
| **CODDEVOL** | Variável N8N | NUMBER | `v_coddevol` | Código devolução |
| **CALCCREDIPI** | Fixo | VARCHAR2 | `'N'` | Calcula crédito IPI |

## 📋 Tabela PCMOVCOMPLE (Complemento da Movimentação - ENTRADA)

**Baseado no arquivo `5.1-GRAVAR-ITENS-ENTRADA.json`**

| Campo Oracle | Campo XML/JSON | Tipo | Valor/Cálculo | Observações |
|--------------|----------------|------|---------------|-------------|
| **NUMTRANSITEM** | Sequence Oracle | NUMBER | `v_numtransitem` | DFSEQ_PCMOVCOMPLE.NEXTVAL |
| **DTREGISTRO** | ide/dhEmi | DATE | `TO_DATE(SUBSTR(dtemissao,1,10), 'DD/MM/YYYY')` | Data registro (apenas data) |
| **ALIQICMS1RET** | det/imposto/ICMS/pST | NUMBER | `pst` | Alíquota ICMS retenção |
| **VLBASEPARTDEST** | det/imposto/ICMSUFDest/vBCUFDest | NUMBER | `vbcufdest` | Base partilha destino |
| **ALIQINTERNADEST** | det/imposto/ICMSUFDest/pICMSUFDest | NUMBER | `picmsufdest` | Alíquota interna destino |
| **ALIQINTERORIGPART** | det/imposto/ICMSUFDest/pICMSInter | NUMBER | `picmsinter` | Alíquota inter origem |
| **PERCPROVPART** | det/imposto/ICMSUFDest/pICMSInterPart | NUMBER | `picmsinterpart` | Percentual provisório |
| **VLICMSPARTDEST** | Calculado | NUMBER | `vlicmspartdest` | Valor ICMS partilha destino |
| **VLICMSPARTREM** | Calculado | NUMBER | `vlicmspartrem` | Valor ICMS partilha remetente |
| **ALIQFCP** | det/imposto/ICMSUFDest/pFCPUFDest | NUMBER | `pfcpufdest` | Alíquota FCP |
| **VLFCPPART** | Calculado | NUMBER | `vlfcppart` | Valor FCP partilha |
| **NUMCHAVEEXP** | protNFe/infProt/chNFe | VARCHAR2 | `chavenfe` | Chave NFe |
| **CODPRODGNRE** | det/prod/cProd | NUMBER | `cprod` | Código produto GNRE |
| **EANCODPROD** | det/prod/cEAN | NUMBER | `NVL(cean, 0)` | EAN código produto |
| **CODSITTRIBIPI** | NULL | VARCHAR2 | `NULL` | Situação tributária IPI |
| **CODTRIBPISCOFINS** | Fixo | NUMBER | `7` | Código tributação PIS/COFINS |
| **NITEMXML** | det/nItem | NUMBER | `nitem` | Número item XML |
| **CODCEST** | det/prod/CEST | VARCHAR2 | `codcest` | Código CEST |
| **ORIGMERCTRIB** | Fixo | NUMBER | `1` | Origem mercadoria tributação |
| **VLBASEFRETE** | Fixo | NUMBER | `0` | Base frete (0 para não duplicar) |
| **VLBASEOUTROS** | Fixo | NUMBER | `0` | Base outros (0 para não duplicar) |

## 📋 Outras Tabelas para Entrada

### PCESTCOM (Estorno Comissão)
- **NUMTRANSENT** - Número transação entrada
- **VLDEVOLUCAO** - Valor devolução
- **VLESTORNO** - Valor estorno
- **DTESTORNO** - Data estorno
- **CODUSUR** - Código usuário
- **CODFUNC** - Código funcionário
- **NUMTRANSVENDA** - Referência nota original
- **HISTORICO** - Histórico
- **VLESTORNOCMV** - Valor estorno CMV

### PCCRECLI (Crédito Cliente)
- **CODCLI** - Código cliente
- **DTLANC** - Data lançamento
- **CODFILIAL** - Código filial
- **VALOR** - Valor
- **NUMNOTA** - Número nota
- **NUMTRANS** - Número transação
- **CODFUNC** - Código funcionário
- **HISTORICO** - Histórico
- **CODFUNCLANC** - Código funcionário lançamento
- **NUMERARIO** - Numerário
- **CODMOVIMENTO** - Código movimento
- **CODROTINA** - Código rotina
- **NUMCRED** - Número crédito

**SEMPRE consulte este arquivo antes de modificar a procedure Oracle para garantir que os campos estão corretos!**
