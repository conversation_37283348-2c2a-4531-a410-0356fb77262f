// =====================================================
// DADOS XML - Script Unificado (Scripts 4 + 5)
// Retorna todos os campos do Script 5 + campos únicos do Script 4
// =====================================================

const xmlData = $json;

try {
  // Validação básica
  if (!xmlData?.nfeProc?.NFe?.infNFe) {
    throw new Error('Estrutura XML inválida');
  }
  
  const nfe = xmlData.nfeProc.NFe.infNFe;
  const ide = nfe.ide;
  const emit = nfe.emit;
  const dest = nfe.dest;
  const total = nfe.total.ICMSTot;
  const protNFe = xmlData.nfeProc.protNFe;
  
  // Extrair chave NFe de forma segura
  let chavenfe = '';
  if (nfe.$ && nfe.$.Id) {
    chavenfe = nfe.$.Id.replace('NFe', '');
  } else if (protNFe?.infProt?.chNFe) {
    chavenfe = protNFe.infProt.chNFe;
  }
  
  // Extrair chave de referência (para devoluções)
  let refnfe = '';
  if (ide.NFref && ide.NFref.refNFe) {
    if (Array.isArray(ide.NFref.refNFe)) {
      refnfe = ide.NFref.refNFe[0] || '';
    } else {
      refnfe = ide.NFref.refNFe || '';
    }
  } else if (ide.NFref && Array.isArray(ide.NFref)) {
    refnfe = ide.NFref[0]?.refNFe || '';
  }

  // DADOS DO CABEÇALHO (Script 4)
  const cabecalho = {
    chavenfe: chavenfe,
    serie: ide.serie,
    numnota: parseInt(ide.nNF),
    tpnf: parseInt(ide.tpNF),
    finnfe: parseInt(ide.finNFe),
    tpimp: parseInt(ide.tpImp),
    modelo: ide.mod,
    natop: ide.natOp,
    dtemissao: ide.dhEmi ? (() => {
      const date = new Date(ide.dhEmi);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    })() : '',
    cstat: protNFe?.infProt?.cStat,
    nprot: protNFe?.infProt?.nProt || '',
    cnpjemitente: emit.CNPJ || emit.CPF,
    ufemitente: emit.enderEmit.UF,
    cnpjdestinatario: dest?.CNPJ || dest?.CPF,
    ufdestinatario: dest?.enderDest?.UF,
    refnfe: refnfe,  // Chave da nota de referência (devoluções)

    // Tipo de pessoa (física ou jurídica)
    tipofj: dest?.CNPJ ? 'J' : dest?.CPF ? 'F' : 'J',

    // Dados adicionais do XML
    codcob: ide.verProc?.includes('mercadolivre') ? 'CML' :
            ide.verProc?.includes('Bling') ? 'D2C' : '',
    infcpl: nfe.infAdic?.infCpl || '',  // Informações complementares

    // DADOS DE ENDEREÇO DO DESTINATÁRIO
    xlgr: dest?.enderDest?.xLgr,
    nro: dest?.enderDest?.nro,
    xbairro: dest?.enderDest?.xBairro,
    xmun: dest?.enderDest?.xMun,
    xpais: dest?.enderDest?.xPais,
    cpais: dest?.enderDest?.cPais,
    cmun: dest?.enderDest?.cMun,
    cep: dest?.enderDest?.CEP,
    indfinal: ide.indFinal,
    indiedest: dest?.indIEDest,

    // TOTAIS DA NFe (Script 4)
    vltotal: parseFloat(total.vNF),
    vprod: parseFloat(total.vProd),
    vicms: parseFloat(total.vICMS || 0),
    vbc: parseFloat(total.vBC || 0),
    vpis: parseFloat(total.vPIS || 0),
    vcofins: parseFloat(total.vCOFINS || 0),
    vipi: parseFloat(total.vIPI || 0),
    vfrete: parseFloat(total.vFrete || 0),
    voutros: parseFloat(total.vOutro || 0),
    vdesc: parseFloat(total.vDesc || 0)
  };
  
  // DADOS DOS ITENS (Script 5 + campos únicos do Script 4)
  let detList = nfe.det;
  if (!Array.isArray(detList)) {
    detList = [detList];
  }
  
  const devolucaoCFOPs = [
    '1201','1202','1203','1204','1208','1209','1212','1213','1214','1215','1216',
    '1410','1411','1503','1504','1505','1506','1553','1660','1661','1662','1918','1919',
    '2201','2202','2203','2204','2208','2209','2212','2213','2214','2215','2216',
    '2410','2411','2503','2504','2505','2506','2553','2660','2661','2662','2918','2919',
    '3201','3202','3211','3212','3503','3553',
    '5201','5202','5208','5209','5210','5213','5214','5215','5216','5410','5411','5412','5413',
    '5503','5553','5555','5556','5660','5661','5662','5918','5919','5921',
    '6201','6202','6208','6209','6210','6213','6214','6215','6216','6410','6411','6412','6413',
    '6503','6553','6555','6556','6660','6661','6662','6918','6919','6921',
    '7201','7202','7210','7211','7212','7553','7556','7930'
  ];
  
  const itens = [];
  
  for (const det of detList) {
    if (det.prod) {
      const nItem = det.$ ? parseInt(det.$.nItem) : parseInt(det.nItem || 1);
      const produto = det.prod;
      const imposto = det.imposto;
      
      // Encontrar tipo de ICMS
      const icms = imposto?.ICMS;
      const icmsObj = [
        'ICMS00', 'ICMS10', 'ICMS20', 'ICMS30', 'ICMS40', 'ICMS50', 'ICMS60', 'ICMS70', 'ICMS80',
        'ICMSSN101', 'ICMSSN102', 'ICMSSN103', 'ICMSSN201', 'ICMSSN202', 'ICMSSN203', 
        'ICMSSN300', 'ICMSSN400', 'ICMSSN500', 'ICMSSN900'
      ].find(obj => icms && icms[obj]);
      
      const icmsData = icms?.[icmsObj] || {};
      
      // PIS/COFINS
      const pis = imposto?.PIS?.PISAliq || imposto?.PIS?.PISOutr || imposto?.PIS;
      const cofins = imposto?.COFINS?.COFINSAliq || imposto?.COFINS?.COFINSOutr || imposto?.COFINS;
      const CST_PIS_COFINS = pis?.CST || cofins?.CST || '00';
      
      // IPI
      const ipi = imposto?.IPI?.IPITrib || imposto?.IPI;
      
      // Detecção de devolução
      const isDevolucao = devolucaoCFOPs.includes(produto.CFOP);
      
      // Cálculos do Script 5
      const qcom = parseFloat(produto.qCom);
      const vuncom = parseFloat(produto.vUnCom);
      const vprod = parseFloat(produto.vProd);
      const vdesc = parseFloat(produto.vDesc || 0);
      const vfrete = parseFloat(produto.vFrete || 0);
      const vipi = parseFloat(ipi?.vIPI || 0);
      const vipidev = parseFloat(ipi?.vIPIDevol || 0);
      const vicms = parseFloat(icmsData.vICMS || 0);
      const vbc = parseFloat(icmsData.vBC || 0);
      
      const vIPIItem = vipi && qcom ? vipi / qcom : 0;
      const vIPIDevol = isDevolucao && vipidev ? vipidev / qcom : 0;
      const vDescItem = vdesc ? vdesc / qcom : 0;
      const vFreteItem = vfrete ? vfrete / qcom : 0;
      
      // PUNITCONT com lógica de devolução (Script 5)
      const PUNITCONT = parseFloat(
        (vuncom + (isDevolucao ? vIPIDevol : vIPIItem) - vDescItem).toFixed(3)
      );
      
      // VLBASEPISCOFINS com lógica de devolução (Script 5)
      const VLBASEPISCOFINS = isDevolucao
        ? (imposto?.PIS?.PISOutr?.vBC ? parseFloat(imposto.PIS.PISOutr.vBC) : 
           imposto?.COFINS?.COFINSOutr?.vBC ? parseFloat(imposto.COFINS.COFINSOutr.vBC) : 0)
        : parseFloat((vprod - vicms).toFixed(2));
      
      // ITEM COMPLETO (Script 5 + campos únicos do Script 4)
      const item = {
        // Campos do Script 5
        nitem: nItem,
        cprod: produto.cProd,
        cean: produto.cEAN || '',
        xprod: produto.xProd,
        ncm: produto.NCM,
        cfop: parseInt(produto.CFOP),
        ucom: produto.uCom,
        qcom: qcom,
        vuncom: vuncom,
        vprod: vprod,
        vdesc: vdesc,
        vfrete: vfrete,
        voutro: parseFloat(produto.vOutro || 0),
        
        // ICMS
        orig: icmsData.orig,
        cst: icmsData.CST,
        picms: parseFloat(icmsData.pICMS || 0),
        vbc: vbc,
        vicms: vicms,
        vbcstret: parseFloat(icmsData.vBCSTRet || 0),
        vicmsstret: parseFloat(icmsData.vICMSSTRet || 0),
        pst: parseFloat(icmsData.pST || 0),
        vicmssubstituto: parseFloat(icmsData.vICMSSubstituto || 0),
        vbcst: parseFloat(icmsData.vBCST || 0),
        picmsst: parseFloat(icmsData.pICMSST || 0),
        pmvast: parseFloat(icmsData.pMVAST || 0),
        vicmsst: parseFloat(icmsData.vICMSST || 0),
        
        // PIS/COFINS
        ppis: parseFloat(pis?.pPIS || 0),
        vbcpis: parseFloat(pis?.vBC || 0),
        vpis: parseFloat(pis?.vPIS || 0),
        pcofins: parseFloat(cofins?.pCOFINS || 0),
        vbccofins: parseFloat(cofins?.vBC || 0),
        vcofins: parseFloat(cofins?.vCOFINS || 0),
        cst_pis_cofins: CST_PIS_COFINS,
        
        // IPI
        pipi: parseFloat(ipi?.pIPI || 0),
        vbcipi: parseFloat(ipi?.vBC || 0),
        vipi: vipi,
        vipidev: vipidev,
        codsittribipi: ipi?.CST || '00',
        
        // DIFAL
        vbcufdest: parseFloat(imposto?.ICMSUFDest?.vBCUFDest || 0),
        picmsufdest: parseFloat(imposto?.ICMSUFDest?.pICMSUFDest || 0),
        picmsinter: parseFloat(imposto?.ICMSUFDest?.pICMSInter || 0),
        picmsinterpart: parseFloat(imposto?.ICMSUFDest?.pICMSInterPart || 0),
        vicmsufdest: parseFloat(imposto?.ICMSUFDest?.vICMSUFDest || 0),
        vicmsufrem: parseFloat(imposto?.ICMSUFDest?.vICMSUFRemet || 0),
        pfcpufdest: parseFloat(imposto?.ICMSUFDest?.pFCPUFDest || 0),
        vfcpufdest: parseFloat(imposto?.ICMSUFDest?.vFCPUFDest || 0),
        
        // CAMPOS CALCULADOS (Script 5)
        punitcont: PUNITCONT,
        vlbasepiscofins: VLBASEPISCOFINS,
        vlbaseipi: vprod / qcom,  // Script 5: por item
        vfreiteitem: vFreteItem,
        vdescitem: vDescItem,
        vipiitem: vIPIItem,
        baseicms: vbc / qcom,
        baseicst: parseFloat(icmsData.vBCST || 0) / qcom,
        st: parseFloat(icmsData.vICMSST || 0) / qcom,
        stbcr: parseFloat(icmsData.vICMSSTRet || 0) / qcom,
        vlicmsbcr: parseFloat(icmsData.vICMSSubstituto || 0) / qcom,
        vlicmspartdest: parseFloat(imposto?.ICMSUFDest?.vICMSUFDest || 0) / qcom,
        vlicmspartrem: parseFloat(imposto?.ICMSUFDest?.vICMSUFRemet || 0) / qcom,
        vlfcppart: parseFloat(imposto?.ICMSUFDest?.vFCPUFDest || 0) / qcom,
        
        // CAMPOS ÚNICOS DO SCRIPT 4
        vprodtotal: parseFloat(total.vProd),  // Script 4: valor total produtos
        vnf: parseFloat(total.vNF),          // Script 4: valor total NFe
        vfretetotal: parseFloat(total.vFrete || 0), // Script 4: frete total
        ufdest: dest?.enderDest?.UF,               // Script 4: UF destino
        ufemit: emit.enderEmit.UF,                 // Script 4: UF emitente
        cgcfilial: emit.CNPJ || emit.CPF,          // Script 4: CNPJ emitente
        cgcent: dest?.CNPJ || dest?.CPF,     // Script 4: CNPJ destinatário
        
        // Campos adicionais
        codcest: produto.CEST || '',
        xped: produto.xPed || '',
        infadprod: det.infAdProd || '',  // Informações adicionais do produto
        is_devolucao: isDevolucao ? 'S' : 'N',
        vbc_pis: parseFloat(pis?.vBC || 0),
        vbc_cofins: parseFloat(cofins?.vBC || 0)
      };
      
      itens.push(item);
    }
  }
  
  // RESULTADO FINAL: Uma linha por item com dados do cabeçalho repetidos
  const resultado = [];

  for (const item of itens) {
    resultado.push({
      // DADOS DO CABEÇALHO (repetidos em cada linha)
      chavenfe: cabecalho.chavenfe,
      serie: cabecalho.serie,
      numnota: cabecalho.numnota,
      tpnf: cabecalho.tpnf,
      finnfe: cabecalho.finnfe,
      tpimp: cabecalho.tpimp,
      modelo: cabecalho.modelo,
      natop: cabecalho.natop,
      dtemissao: cabecalho.dtemissao,
      cstat: cabecalho.cstat,
      nprot: cabecalho.nprot,
      cnpjemitente: cabecalho.cnpjemitente,
      ufemitente: cabecalho.ufemitente,
      cnpjdestinatario: cabecalho.cnpjdestinatario,
      ufdestinatario: cabecalho.ufdestinatario,
      refnfe: cabecalho.refnfe,  // Chave da nota de referência (devoluções)
      tipofj: cabecalho.tipofj,

      // Dados adicionais
      codcob: cabecalho.codcob,
      infcpl: cabecalho.infcpl,

      // DADOS DE ENDEREÇO DO DESTINATÁRIO
      xlgr: cabecalho.xlgr,
      nro: cabecalho.nro,
      xbairro: cabecalho.xbairro,
      xmun: cabecalho.xmun,
      xpais: cabecalho.xpais,
      cpais: cabecalho.cpais,
      cmun: cabecalho.cmun,
      cep: cabecalho.cep,
      indfinal: cabecalho.indfinal,
      indiedest: cabecalho.indiedest,

      vltotal: cabecalho.vltotal,
      vprod: cabecalho.vprod,
      vicms: cabecalho.vicms,
      vbc: cabecalho.vbc,
      vpis: cabecalho.vpis,
      vcofins: cabecalho.vcofins,
      vipi: cabecalho.vipi,
      vfrete: cabecalho.vfrete,
      voutros: cabecalho.voutros,
      vdesc: cabecalho.vdesc,

      // DADOS DO ITEM (específicos de cada linha)
      nitem: item.nitem,
      cprod: item.cprod,
      cean: item.cean,
      xprod: item.xprod,
      ncm: item.ncm,
      cfop: item.cfop,
      ucom: item.ucom,
      qcom: item.qcom,
      vuncom: item.vuncom,
      vprod_item: item.vprod,  // vprod do item (diferente do total)
      vdesc_item: item.vdesc,  // vdesc do item (diferente do total)
      vfrete_item: item.vfrete, // vfrete do item (diferente do total)
      voutro: item.voutro,

      // ICMS
      orig: item.orig,
      cst: item.cst,
      picms: item.picms,
      vbc_item: item.vbc,      // vbc do item (diferente do total)
      vicms_item: item.vicms,  // vicms do item (diferente do total)
      vbcstret: item.vbcstret,
      vicmsstret: item.vicmsstret,
      pst: item.pst,
      vicmssubstituto: item.vicmssubstituto,
      vbcst: item.vbcst,
      picmsst: item.picmsst,
      pmvast: item.pmvast,
      vicmsst: item.vicmsst,

      // PIS/COFINS
      ppis: item.ppis,
      vbcpis: item.vbcpis,
      vpis_item: item.vpis,    // vpis do item (diferente do total)
      pcofins: item.pcofins,
      vbccofins: item.vbccofins,
      vcofins_item: item.vcofins, // vcofins do item (diferente do total)
      cst_pis_cofins: item.cst_pis_cofins,

      // IPI
      pipi: item.pipi,
      vbcipi: item.vbcipi,
      vipi_item: item.vipi,    // vipi do item (diferente do total)
      vipidev: item.vipidev,
      codsittribipi: item.codsittribipi,

      // DIFAL
      vbcufdest: item.vbcufdest,
      picmsufdest: item.picmsufdest,
      picmsinter: item.picmsinter,
      picmsinterpart: item.picmsinterpart,
      vicmsufdest: item.vicmsufdest,
      vicmsufrem: item.vicmsufrem,
      pfcpufdest: item.pfcpufdest,
      vfcpufdest: item.vfcpufdest,

      // CAMPOS CALCULADOS
      punitcont: item.punitcont,
      vlbasepiscofins: item.vlbasepiscofins,
      vlbaseipi: item.vlbaseipi,
      vfreiteitem: item.vfreiteitem,
      vdescitem: item.vdescitem,
      vipiitem: item.vipiitem,
      baseicms: item.baseicms,
      baseicst: item.baseicst,
      st: item.st,
      stbcr: item.stbcr,
      vlicmsbcr: item.vlicmsbcr,
      vlicmspartdest: item.vlicmspartdest,
      vlicmspartrem: item.vlicmspartrem,
      vlfcppart: item.vlfcppart,

      // CAMPOS ÚNICOS DO SCRIPT 4
      vprodtotal: item.vprodtotal,
      vnf: item.vnf,
      vfretetotal: item.vfretetotal,
      ufdest: item.ufdest,
      ufemit: item.ufemit,
      cgcfilial: item.cgcfilial,
      cgcent: item.cgcent,

      // CAMPOS ADICIONAIS
      codcest: item.codcest,
      xped: item.xped,
      infadprod: item.infadprod,
      is_devolucao: item.is_devolucao,
      vbc_pis: item.vbc_pis,
      vbc_cofins: item.vbc_cofins
    });
  }

  return resultado;

} catch (error) {
  return [{
    erro: true,
    mensagem: error.message,
    stack: error.stack
  }];
}
