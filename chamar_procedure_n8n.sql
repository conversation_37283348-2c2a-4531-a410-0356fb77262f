declare
   v_retorno    varchar2(4000);
   v_dados_json clob;
begin
  -- Construir JSON com dados do dados_xml.js
  -- IMPORTANTE: No N8N, usar {{ $json.toJsonString() }} para converter objeto em string JSON válida
   v_dados_json := '[{{ $json.toJsonString() }}]';
   dbms_output.put_line('🚀 Iniciando processamento da NFe...');
   dbms_output.put_line('📊 Tamanho do JSON: '
                        || length(v_dados_json)
                        || ' caracteres');

  -- Chamar a procedure com dados JSON
   nervsflow_integradora(
      v_dados_json,
      v_retorno
   );

  -- Exibir resultado
   dbms_output.put_line('📤 Resultado: ' || v_retorno);

  -- Verificar se houve sucesso
   if v_retorno like 'SUCCESS%' then
      commit;
      dbms_output.put_line('✅ NFe processada com sucesso!');

    -- Verificar dados gravados
      dbms_output.put_line('📋 Verificando dados gravados...');
      for rec in (
         select count(*) as total
           from pcmov
          where trunc(dtmov) = trunc(sysdate)
      ) loop
         dbms_output.put_line('📦 Total de itens em PCMOV hoje: ' || rec.total);
      end loop;

   else
      rollback;
      dbms_output.put_line('❌ Erro na procedure: ' || v_retorno);
      raise_application_error(
         -20001,
         'Erro na procedure: ' || v_retorno
      );
   end if;

exception
   when others then
      dbms_output.put_line('💥 ERRO GERAL: ' || sqlerrm);
      rollback;
      raise;
end;
