# Como Usar o Novo Script no N8N

## 🎯 **Problema Resolvido**

O `dados_xml.js` original retornava **múltiplas linhas** (uma para cada item), fazendo com que o N8N chamasse a procedure Oracle **múltiplas vezes**, causando erro de duplicidade.

## ✅ **Nova Solução - UM ARQUIVO SÓ**

### **Fluxo N8N Simplificado:**

```
1. [Trigger/Input] → XML da NFe
2. [Code Node] → dados_xml_oracle.js (retorna cabecalho + itens)
3. [Code Node] → Preparar JSON para Oracle
4. [Oracle Node] → Chamar procedure UMA VEZ SÓ
```

---

## 📝 **Configuração dos Nós N8N**

### **Nó 1: Extração e Conversão**
- **Tipo**: Code (JavaScript)
- **Arquivo**: `dados_xml_oracle.js`
- **Input**: XML da NFe
- **Output**: Array JSON pronto para procedure Oracle

### **Nó 2: Chamada Oracle**
- **Tipo**: Oracle Database
- **Procedure**: `nervsflow_integradora`
- **Input**: `JSON.stringify({{ $json }})`
- **Execuções**: **UMA VEZ SÓ** (não em loop)

---

## 🔧 **Vantagens da Nova Abordagem**

### ✅ **Antes (Problema):**
```
dados_xml.js → [item1, item2, item3, ...]
                   ↓      ↓      ↓
              Oracle  Oracle  Oracle  ← MÚLTIPLAS CHAMADAS
              (erro)  (erro)  (erro)
```

### ✅ **Depois (Solução):**
```
dados_xml_oracle.js → [item1+cab, item2+cab, item3+cab]
                                    ↓
                              Oracle UMA VEZ ← SUCESSO!
```

---

## 📊 **Estrutura dos Dados**

### **dados_xml_oracle.js Output:**
```json
[
  {
    "chavenfe": "31250105330305000509550010000005801205371625",
    "serie": "1",
    "numnota": 580,
    "vltotal": 1500.00,
    "nitem": 1,
    "cprod": "PROD001",
    "qcom": 2,
    ...
  },
  {
    "chavenfe": "31250105330305000509550010000005801205371625", 
    "serie": "1",
    "numnota": 580,
    "vltotal": 1500.00,
    "nitem": 2,
    "cprod": "PROD002",
    "qcom": 1,
    ...
  }
]
```

---

## 🚀 **Resultado Final**

- ✅ **UMA chamada** para a procedure Oracle
- ✅ **Cabeçalho processado** apenas uma vez
- ✅ **Todos os itens** processados em uma única transação
- ✅ **Sem erros** de duplicidade
- ✅ **Performance otimizada**

---

## 📊 **Estrutura dos Dados**

### **Nó 1 - dados_xml_oracle.js Output:**
```json
{
  "cabecalho": {
    "chavenfe": "31250105330305000509550010000005801205371625",
    "serie": "1",
    "numnota": 580,
    "tpnf": 1,
    "vltotal": 1500.00,
    "cnpjemitente": "05330305000509",
    "cnpjdestinatario": "12345678000195",
    ...
  },
  "itens": [
    {
      "chavenfe": "31250105330305000509550010000005801205371625",
      "serie": "1",
      "numnota": 580,
      "vltotal": 1500.00,
      "nitem": 1,
      "cprod": "PROD001",
      "qcom": 2,
      ...
    },
    {
      "chavenfe": "31250105330305000509550010000005801205371625",
      "serie": "1",
      "numnota": 580,
      "vltotal": 1500.00,
      "nitem": 2,
      "cprod": "PROD002",
      "qcom": 1,
      ...
    }
  ],
  "total_itens": 2
}
```

### **Nó 2 - Preparar para Oracle Output:**
```json
{
  "dados_json": "[{\"chavenfe\":\"31250105330305000509550010000005801205371625\",\"serie\":\"1\",\"numnota\":580,\"nitem\":1,\"cprod\":\"PROD001\",...},{\"chavenfe\":\"31250105330305000509550010000005801205371625\",\"serie\":\"1\",\"numnota\":580,\"nitem\":2,\"cprod\":\"PROD002\",...}]"
}
```

---

## 🔄 **Migração do Fluxo Atual**

1. **Substitua** o nó `dados_xml.js` por `dados_xml_oracle.js`
2. **Adicione** um nó Code para preparar o JSON: `return { dados_json: JSON.stringify($json.itens) };`
3. **Configure** o nó Oracle para receber: `{{ $json.dados_json }}`
4. **Remova** qualquer loop no N8N (não é mais necessário)
5. **Teste** com uma NFe de exemplo

---

## ⚠️ **Importante**

- A procedure Oracle já está preparada para receber esse formato
- Não é necessário alterar a procedure
- O processamento será mais rápido e confiável
- Mantenha os scripts originais como backup
