/*
ESTE BLOCO DEVE SER EXECUTADO PRIMEIRO PARA QUE 
SEJA FEITA A CONSOLIDA��O DAS NFS PRESENTES NA PCNFSAIDPREFAT
*/

DECLARE
BEGIN
  FOR DADOS IN (SELECT NUMTRANSVENDA
                  FROM PCNFSAIDPREFAT
                 WHERE NUMTRANSVENDA IN (:NUM_TRA<PERSON>ACAO)) LOOP
  
    DECLARE
      v_TRANSACAO NUMBER;
      v_MSGEST    VARCHAR2(100);
      v_RETORNO   NUMBER(10);
      v_OWNER     VARCHAR2(15);
    
      PROCEDURE FASTCONSOLIDATE(v_TABLE         IN VARCHAR2,
                                v_NUM<PERSON>ANSVENDA IN NUMBER) IS
        v_SCRIPT VARCHAR2(32767);
      BEGIN
        v_OWNER := 'MIMOTESTE';  
        v_SCRIPT := 'INSERT INTO ' || UPPER(v_TABLE) || ' (';
      
        FOR COLUNAS IN (SELECT A.COLUMN_NAME, ROWNUM IDR
                          FROM ALL_TAB_COLUMNS A
                         WHERE A.TABLE_NAME = UPPER(v_TABLE)
                           AND EXISTS
                         (SELECT 1
                            FROM ALL_TAB_COLUMNS C
                           WHERE C.COLUMN_NAME = A.COLUMN_NAME
                             AND C.TABLE_NAME = UPPER(v_TABLE) || 'PREFAT'
                             AND C.OWNER = v_OWNER)
                             AND A.OWNER = v_OWNER
                         ORDER BY ROWNUM) LOOP
        
          IF COLUNAS.IDR = 1 THEN
            v_SCRIPT := v_SCRIPT || COLUNAS.COLUMN_NAME || CHR(13);
          ELSE
            v_SCRIPT := v_SCRIPT || ',' || COLUNAS.COLUMN_NAME || CHR(13);
          END IF;
        END LOOP;
      
        v_SCRIPT := v_SCRIPT || ' ) SELECT   ';
      
        FOR COLUNAS IN (SELECT A.COLUMN_NAME, A.TABLE_NAME, ROWNUM IDR
                          FROM ALL_TAB_COLUMNS A
                         WHERE A.TABLE_NAME = UPPER(v_TABLE)
                           AND EXISTS
                         (SELECT 1
                            FROM ALL_TAB_COLUMNS C
                           WHERE C.COLUMN_NAME = A.COLUMN_NAME
                             AND C.TABLE_NAME = UPPER(v_TABLE) || 'PREFAT'
                             AND C.OWNER = v_OWNER)
                             AND A.OWNER = v_OWNER
                         ORDER BY ROWNUM) LOOP
        
          IF COLUNAS.IDR = 1 THEN
            v_SCRIPT := v_SCRIPT || UPPER(v_TABLE) || 'PREFAT' || '.' ||
                        COLUNAS.COLUMN_NAME || CHR(13);
          ELSE
            v_SCRIPT := v_SCRIPT || ',' || UPPER(v_TABLE) || 'PREFAT' || '.' ||
                        COLUNAS.COLUMN_NAME || CHR(13);
          END IF;
        
        END LOOP;
      
        IF INSTR(UPPER(v_TABLE), 'PCMOVCOMPLE') > 0 THEN
          v_SCRIPT := v_SCRIPT ||
                      'FROM PCMOVPREFAT, PCMOVCOMPLEPREFAT WHERE PCMOVPREFAT.NUMTRANSITEM = PCMOVCOMPLEPREFAT.NUMTRANSITEM' ||
                      ' AND PCMOVPREFAT.NUMTRANSVENDA = ' ||
                      v_NUMTRANSVENDA;
        ELSE
          v_SCRIPT := v_SCRIPT || 'FROM ' || UPPER(v_TABLE) ||
                      'PREFAT WHERE NUMTRANSVENDA = ' || v_NUMTRANSVENDA;
        END IF;
      
        EXECUTE IMMEDIATE v_SCRIPT;
     
      END;
    BEGIN
      v_TRANSACAO := DADOS.NUMTRANSVENDA;
    
      FASTCONSOLIDATE('PCMOV', v_TRANSACAO);
      FASTCONSOLIDATE('PCMOVCOMPLE', v_TRANSACAO);
      FASTCONSOLIDATE('PCNFSAID', v_TRANSACAO);
      FASTCONSOLIDATE('PCNFBASE', v_TRANSACAO);
      FASTCONSOLIDATE('PCPREST', v_TRANSACAO);
      FASTCONSOLIDATE('PCLANC', v_TRANSACAO);
    
      UPDATE PCNFSAID
         SET SITUACAONFE = '100',
             ESPECIE     = 'NF',
             TIPOEMISSAO = '1',
             ENVIADA     = 'S',
             AMBIENTENFE = 'H',
             NUMVIAS     = '0'
       WHERE NUMTRANSVENDA = DADOS.NUMTRANSVENDA;
    
      v_RETORNO := PKG_ESTOQUE.VENDAS_SAIDA(v_TRANSACAO, 'N', v_MSGEST);
    
    END;
  
  END LOOP;

END;

/*
ESTE BLOCO DEVE SER EXECUTADO 
AP�S A CONSOLIDA��O DOS DADOS
*/
BEGIN

  DELETE FROM PCNFSAIDPREFAT T    WHERE T.NUMTRANSVENDA IN (:NUM_TRANSACAO);
  DELETE FROM PCMOVCOMPLEPREFAT T WHERE T.NUMTRANSITEM  IN (SELECT NUMTRANSITEM FROM PCMOV WHERE NUMTRANSVENDA IN (:NUM_TRANSACAO));
  DELETE FROM PCMOVPREFAT T       WHERE T.NUMTRANSVENDA IN (:NUM_TRANSACAO);
  DELETE FROM PCPRESTPREFAT T     WHERE T.NUMTRANSVENDA IN (:NUM_TRANSACAO);

END;
/* 
 FIM
*/