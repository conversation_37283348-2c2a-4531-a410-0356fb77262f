﻿CREATE PROCEDURE importar_doceletronico(p_retorno OUT VARCHAR2)
  IS
    v_chavenfe            VARCHAR2(50);
    v_rowid               VARCHAR2(50);
    i                     INTEGER;
    v_numtrans            INTEGER;
    v_tpnf                INTEGER;
    v_finnfe              INTEGER;
    v_codparceiro         INTEGER;
    v_tpimp               INTEGER;
    v_codfilial           VARCHAR2(2);
    v_Enderdestinatario   VARCHAR2(60);
    v_Ender_Numero        VARCHAR2(20);
    v_Bairro_Destinatario VARCHAR2(40);

    v_custofin            NUMBER;
    v_custoreal           NUMBER;
    v_custorep            NUMBER;
    v_custocont           NUMBER;
    v_dtesthist           DATE;

    v_cmv                 NUMBER;
    v_cmvpf               NUMBER;
    v_codst               INTEGER;
    v_tribuf              VARCHAR2(1);

    nfsaida               pcnfsaid % ROWTYPE;
    nfentrada             pcnfent % ROWTYPE;
    credito               pccrecli % ROWTYPE;
    mov                   pcmov % ROWTYPE;
    movcomple             pcmovcomple % ROWTYPE;
    prest                 pcprest % ROWTYPE;

    v_entrada_saida       VARCHAR2(10);
    v_natop               VARCHAR2(400);

    v_ufemitente          VARCHAR2(2);
    v_ufdestinatario      VARCHAR2(2);
    v_cnpjemitente        VARCHAR2(14);
    v_cnpjdestinatrio     VARCHAR2(14);
    v_serie               VARCHAR2(3);
    v_numnota             INTEGER;
    v_dtsaient            VARCHAR2(25);
    v_vltotal             VARCHAR2(25);
    v_vlfrete             VARCHAR2(25);
    v_vloutros            VARCHAR2(25);
    v_vst                 VARCHAR2(25);
    v_vbcst               VARCHAR2(25);
    v_vipi                VARCHAR2(25);
    v_vipidev             VARCHAR2(25);
    v_vcofins             VARCHAR2(25);
    v_vpis                VARCHAR2(25);
    v_xpais               VARCHAR2(25);
    v_cpais               VARCHAR2(25);
    v_cmun                VARCHAR2(25);
    v_cep                 VARCHAR2(25);

    v_vbcudest            VARCHAR2(25);
    v_picmsufdest         VARCHAR2(25);
    v_picmsinter          VARCHAR2(25);
    v_picmsinterpart      VARCHAR2(25);
    v_icmsufdest          VARCHAR2(25);

    v_xmun                VARCHAR2(120);
    v_xmlns               VARCHAR2(50)
    DEFAULT 'xmlns="http://www.portalfiscal.inf.br/nfe"';

    consum                pcconsum % ROWTYPE;

    v_msg                 VARCHAR2(5000);
    v_retorno             VARCHAR2(5000);

    xmlnfe                CLOB;
  BEGIN

    dbms_output.put_line('Inicando a Importacao....');

    SELECT *
      INTO consum
      FROM pcconsum;

    SELECT XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/@Id', v_xmlns).getstringval()
           v_chavenfe,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/tpNF/text()', v_xmlns).getstringval()
           v_tpnf,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/finNFe/text()',
           v_xmlns).getstringval()
           v_finnfe,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/tpImp/text()',
           v_xmlns).getstringval()
           v_tpimp,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/natOp/text()',
           v_xmlns).getstringval()
           v_natop,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/emit/enderEmit/UF/text()',
           v_xmlns).getstringval()
           v_ufemitente,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/UF/text()',
           v_xmlns).getstringval()
           v_ufdestinatario,

           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/xLgr/text()',
           v_xmlns).getstringval()
           v_Enderdestinatario,

           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/nro/text()',
           v_xmlns).getstringval()
           v_Ender_Numero,

           SUBSTR(XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/xBairro/text()',
           v_xmlns).getstringval(), 1, 40)
           v_Bairro_Destinatario,

           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/xMun/text()',
           v_xmlns).getstringval()
           v_xmun,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/xPais/text()',
           v_xmlns).getstringval()
           v_xpais,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/cPais/text()',
           v_xmlns).getstringval()
           v_cpais,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/cMun/text()',
           v_xmlns).getstringval()
           v_cmun,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/enderDest/CEP/text()',
           v_xmlns).getstringval()
           v_cep,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/dest/CNPJ/text()',
           v_xmlns).getstringval()
           v_cnpjdestinatrio,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/emit/CNPJ/text()',
           v_xmlns).getstringval()
           v_cnpjemitente,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/serie/text()',
           v_xmlns).getstringval()
           serie,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/nNF/text()', v_xmlns).getstringval()
           numnota,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/ide/dhEmi/text()',
           v_xmlns).getstringval()
           dtsaient,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vNF/text()',
           v_xmlns).getstringval()
           vltotal,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vFrete/text()',
           v_xmlns).getstringval()
           vlfrete,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vOutro/text()',
           v_xmlns).getstringval()
           vloutros,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vST/text()',
           v_xmlns).getstringval()
           v_vst,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vBCST/text()',
           v_xmlns).getstringval()
           v_vbcst,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vIPI/text()',
           v_xmlns).getstringval()
           v_vipi,

           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vIPIDevol/text()',
           v_xmlns).getstringval()
           v_vipidev,

           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vCOFINS/text()',
           v_xmlns).getstringval()
           v_vcofins,
           XMLTYPE(w.conteudo).extract('nfeProc/NFe/infNFe/total/ICMSTot/vPIS/text()',
           v_xmlns).getstringval()
           v_vpis,
           w.ROWID
      INTO v_chavenfe,
           v_tpnf,
           v_finnfe,
           v_tpimp,
           v_natop,
           v_ufemitente,
           v_ufdestinatario,
           v_Enderdestinatario,
           v_Ender_Numero,
           v_Bairro_Destinatario,
           v_xmun,
           v_xpais,
           v_cpais,
           v_cmun,
           v_cep,
           v_cnpjdestinatrio,
           v_cnpjemitente,
           v_serie,
           v_numnota,
           v_dtsaient,
           v_vltotal,
           v_vlfrete,
           v_vloutros,
           v_vst,
           v_vbcst,
           v_vipi,
           v_vipidev,
           v_vcofins,
           v_vpis,
           v_rowid
      FROM arquivosxml w;

    v_chavenfe := apenasnumeros(v_chavenfe);

    dbms_output.put_line('Chave: ' || v_chavenfe);
    dbms_output.put_line('Total: ' || v_vltotal);
    dbms_output.put_line('Cnpj Emitente: ' || v_cnpjemitente);

    v_codfilial := '';
    v_entrada_saida := '';

    IF v_cnpjdestinatrio = '05330305000690'
    THEN
      v_codfilial := '6';
      v_entrada_saida := 'entrada';
    ELSE
      IF v_cnpjemitente = '05330305000690'
      THEN
        v_codfilial := '6';
        v_entrada_saida := 'saida';

        IF (LOWER(v_natop) = 'outras entradas - retorno simbolico de deposito temporario')
          OR (v_tpnf = 0)
        THEN
          v_Entrada_Saida := 'entrada';
        END IF;
      END IF;
    END IF;

    IF (v_codfilial = '')
      OR (v_codfilial IS NULL)
    THEN
      IF v_cnpjdestinatrio = '05330305000185'
      THEN
        v_codfilial := '1';
        v_entrada_saida := 'entrada';
      ELSE
        IF v_cnpjemitente = '05330305000185'
        THEN
          v_codfilial := '1';
          v_entrada_saida := 'saida';

          IF (LOWER(v_natop) = 'outras entradas - retorno simbolico de deposito temporario')
            OR (v_tpnf = 0)
          THEN
            v_Entrada_Saida := 'entrada';
          END IF;
        END IF;
      END IF;
    END IF;

    IF v_codfilial = ''
    THEN
      p_retorno := 'XML NF-e não autorizada (' || v_chavenfe || ')';
      INSERT INTO arquivosxmlproc (
        data, nome, conteudo, retorno
      )
      SELECT data,
             nome,
             conteudo,
             p_retorno
        FROM arquivosxml;
      COMMIT;
      RETURN;
    END IF;
    dbms_output.put_line('Filial: ' || v_codfilial);

    IF v_entrada_saida = 'saida'
    THEN
      SELECT COUNT(*),
             MAX(numtransvenda)
        INTO i,
             v_numtrans
        FROM pcnfsaid
        WHERE chavenfe = v_chavenfe
          AND dtcancel IS NULL
          AND codfilial = v_codfilial;

      IF i > 0
      THEN
        p_retorno := 'XML saida NF-e ja processado anteriormente (' || v_chavenfe || ', Num.Trans.Venda:' || v_numtrans || ')';
        INSERT INTO arquivosxmlproc (
          data, nome, conteudo, retorno
        )
        SELECT data,
               nome,
               conteudo,
               p_retorno
          FROM arquivosxml;
        COMMIT;
        RETURN;
      END IF;

    END IF;

    SELECT COUNT(*),
           MAX(numtransent)
      INTO i,
           v_numtrans
      FROM pcnfent
      WHERE chavenfe = v_chavenfe
        AND dtcancel IS NULL;

    IF i > 0
    THEN
      p_retorno := 'XML entrada NF-e ja processado anteriormente(' || v_chavenfe || ', transaçao:' || v_numtrans || ')';
      INSERT INTO arquivosxmlproc (
        data, nome, conteudo, retorno
      )
      SELECT data,
             nome,
             conteudo,
             p_retorno
        FROM arquivosxml;
      COMMIT;

      RETURN;
    END IF;

    IF UPPER(v_natop) LIKE '%ZZZZZ%'
    THEN
      p_retorno := 'Nota fiscal de servico';
      INSERT INTO arquivosxmlproc (
        data, nome, conteudo, retorno
      )
      SELECT data,
             nome,
             conteudo,
             p_retorno
        FROM arquivosxml;
      COMMIT;

      RETURN;
    END IF;

    IF v_entrada_saida = 'saida'
    THEN                                      -- Dados da Nota Fiscal de Saida
      nfsaida.ESPECIE := 'NF';

      SELECT proxnumtransvenda
        INTO nfsaida.numtransvenda
        FROM pcconsum
      FOR UPDATE NOWAIT;

      UPDATE PCCONSUM
        SET PROXNUMTRANSVENDA = PROXNUMTRANSVENDA + 1;

      COMMIT;

      nfsaida.tipoemissao   := v_tpimp;
      nfsaida.finalidadenfe := v_finnfe;
      nfsaida.caixa         := 0;
      nfsaida.serie         := v_serie;
      nfsaida.numnota       := v_numnota;
      nfsaida.vlfrete       := numero(v_vlfrete);
      nfsaida.vloutrasdesp  := numero(v_vloutros);
      nfsaida.dtsaida       := trunc(xmldate(v_dtsaient));
      nfsaida.dtentrega     := trunc(nfsaida.dtsaida);
      nfsaida.vltotal       := numero(v_vltotal);
      nfsaida.icmsretido    := numero(v_vst);
      nfsaida.bcst          := numero(v_vbcst);
      nfsaida.vlipi         := numero(v_vipi);
      nfsaida.vlcofins      := numero(v_vcofins);
      nfsaida.vlpis         := numero(v_vpis);
      nfsaida.codpais       := numero(v_cpais);
      nfsaida.descpais      := v_xpais;
      nfsaida.cep           := numeros(v_cep);
      nfsaida.uf            := v_ufdestinatario;

      -- NOTAS DE REMESSA NAO DEVEM APARECER NA ROTINA 111
      IF LOWER(v_natop) LIKE '%venda%'
      THEN
        nfsaida.tipovenda := 1;
      ELSE
        nfsaida.tipovenda := 'SR';
      END IF;

      SELECT NVL(MAX(codcidade), 0)
        INTO nfsaida.codmunicipio
        FROM pccidade
        WHERE codibge = v_cmun;

      NFSAIDA.MUNICIPIO := SUBSTR(V_XMUN, 1, 15);
      NFSAIDA.ENDERECO := SUBSTR(V_ENDERDESTINATARIO, 1, 40);
      NFSAIDA.NUMENDERECO := SUBSTR(V_ENDER_NUMERO, 1, 5);
      NFSAIDA.BAIRRO := SUBSTR(V_BAIRRO_DESTINATARIO, 1, 20);

      NFSAIDA.VLTOTGER := NFSAIDA.VLTOTAL;
      NFSAIDA.CHAVENFE := V_CHAVENFE;
      NFSAIDA.PRAZOPONDERADO := 'N';
      NFSAIDA.PERBASEREDOUTRASDESP := 0;
      NFSAIDA.GERACP := 'N';
      NFSAIDA.CODFISCALFRETE := 0;
      NFSAIDA.PERCICMFRETE := 0;
      NFSAIDA.AMBIENTENFE := 'P';
      NFSAIDA.CONFERIDO := 'N';
      NFSAIDA.AGREGASTVLMERC := 'N';
      NFSAIDA.EMISSNUMAUTOMATICO := 'N';
      NFSAIDA.CTEREGIMEESPECIAL := 'N';
      --NFSAIDA.DTDC := TRUNC (SYSDATE);
      NFSAIDA.NFIPIEMITIDA := 'N';
      NFSAIDA.REDUZICMSDOCTE := 'N';
      NFSAIDA.GERARBCRNFE := 'S';
      NFSAIDA.ALIQICMOUTRASDESP := 0;
      NFSAIDA.CODFISCALOUTRASDESP := 0;
      NFSAIDA.CODSITTRIBPISCOFINS := 1;
      NFSAIDA.CODFISCALNF := NULL;
      NFSAIDA.DTLANCTO := TRUNC(SYSDATE);
      NFSAIDA.VLBASEIPI := 0;
      NFSAIDA.PERCOFINS := 0;
      NFSAIDA.PERPIS := 0;
      NFSAIDA.VLBASEPISCOFINS := 0;
      NFSAIDA.CODCOB := 'CARC';
      NFSAIDA.CODPLPAG := 1;
      NFSAIDA.CONDVENDA := 1;
      NFSAIDA.NUMCAR := 0;

      SELECT MAX(codcli)
        INTO v_codparceiro
        FROM pcclient
        WHERE apenasnumeros(cgcent) = v_cnpjdestinatrio;

      IF v_codparceiro IS NULL
      THEN
        importar_participante_xml_nfe(v_codparceiro);
      END IF;

      NFSAIDA.CODCLI := V_CODPARCEIRO;
      NFSAIDA.CODCLINF := V_CODPARCEIRO;
      NFSAIDA.CODFILIAL := V_CODFILIAL;
      NFSAIDA.CODFILIALNF := V_CODFILIAL;
      NFSAIDA.MUNICIPIO := SUBSTR(V_XMUN, 1, 30);


      IF v_finnfe = 4
      THEN                                        -- Devolucao de Fornecedor
        nfsaida.codcont := consum.codcontfor;

        IF v_ufdestinatario = v_ufemitente
        THEN
          nfsaida.codfiscal := 521;
        ELSE
          nfsaida.codfiscal := 621;
        END IF;
      ELSE                                          -- Demais Notas de Saida
        DBMS_OUTPUT.put_line('Cod.Parceiro: ' || v_codparceiro);

        SELECT codusur1,
               codpraca
          INTO nfsaida.codusur,
               nfsaida.codpraca
          FROM pcclient
          WHERE codcli = v_codparceiro;

        nfsaida.CODCONT := consum.codcontcli;

        IF v_ufdestinatario = v_ufemitente
        THEN
          nfsaida.codfiscal := 512;
        ELSE
          nfsaida.codfiscal := 612;
        END IF;
      END IF;

      nfsaida.codfiscal := 76;
      nfsaida.codusur := 802;

      SELECT NVL(MAX(codsupervisor), 0)
        INTO nfsaida.codsupervisor
        FROM pcusuari
        WHERE codusur = nfsaida.codusur;

      nfsaida.codsupervisor := nfsaida.codsupervisor;

      MOV.CODOPER := 'S';
      MOV.DTMOV := TRUNC(NFSAIDA.DTSAIDA);
      MOV.DTMOVLOG := MOV.DTMOV;
      MOV.NUMTRANSVENDA := NFSAIDA.NUMTRANSVENDA;
      MOV.NUMNOTA := NFSAIDA.NUMNOTA;
      MOV.CODFILIAL := NFSAIDA.CODFILIAL;
      MOV.CODCLI := V_CODPARCEIRO;

      INSERT INTO pcnfsaid
      VALUES nfsaida;

      IF LOWER(v_natop) LIKE '%venda%'
      THEN
        PREST.NUMTRANSVENDA := MOV.NUMTRANSVENDA;
        PREST.VALOR := NFSAIDA.VLTOTAL;
        PREST.PREST := '1';
        PREST.DUPLIC := NFSAIDA.NUMNOTA;
        PREST.DTVENC := TRUNC(SYSDATE) + 30;
        PREST.DTVENCORIG := TRUNC(NFSAIDA.DTSAIDA) + 30;
        PREST.DTEMISSAO := TRUNC(NFSAIDA.DTSAIDA);
        PREST.CODCOB := 'CML';
        PREST.CODCOBORIG := 'CML';
        PREST.CODFILIAL := NFSAIDA.CODFILIAL;
        PREST.STATUS := 'A';
        PREST.VALORORIG := NFSAIDA.VLTOTAL;
        PREST.TXPERM := 0;
        PREST.OPERACAO := 'S';
        PREST.CODUSUR := NFSAIDA.CODUSUR;
        PREST.CODCLI := V_CODPARCEIRO;
        INSERT INTO pcprest
        VALUES prest;
      END IF;

    END IF;


    IF v_entrada_saida = 'entrada'
    THEN                                    -- Dados da Nota Fiscal de Entrada
      nfentrada.especie := 'NF';

      SELECT PROXNUMTRANSENT
        INTO nfentrada.numtransent
        FROM pcconsum
      FOR UPDATE NOWAIT;

      UPDATE PCCONSUM
        SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;

      COMMIT;

      NFENTRADA.DTEMISSAO := TRUNC(XMLDATE(V_DTSAIENT));
      NFENTRADA.TIPODESCARGA := '2';
      NFENTRADA.SERIE := V_SERIE;
      NFENTRADA.NUMNOTA := V_NUMNOTA;
      NFENTRADA.VLFRETE := NUMERO(V_VLFRETE);
      NFENTRADA.DTENT := TRUNC(XMLDATE(V_DTSAIENT));
      NFENTRADA.VLTOTAL := NUMERO(V_VLTOTAL);
      NFENTRADA.VLST := NUMERO(V_VST);
      NFENTRADA.BASEICST := NUMERO(V_VBCST);
      NFENTRADA.VLIPI := NUMERO(V_VIPI);

      IF NVL(numero(v_vipidev), 0) > 0
      THEN
        nfentrada.vlipi := numero(v_vipidev);
      END IF;

      nfentrada.chavenfe := v_chavenfe;

      SELECT MIN(codfornec)
        INTO v_codparceiro
        FROM pcfornec
        WHERE apenasnumeros(cgc) = v_cnpjemitente;

      IF v_finnfe = '4'
      THEN
        nfentrada.TIPODESCARGA := '6';

        IF (v_cnpjemitente = '05330305000185')
          OR (v_cnpjemitente = '05330305000690')
        THEN
          SELECT MAX(codcli)
            INTO v_codparceiro
            FROM pcclient
            WHERE apenasnumeros(cgcent) = v_cnpjdestinatrio;
        ELSE
          SELECT MAX(codcli)
            INTO v_codparceiro
            FROM pcclient
            WHERE apenasnumeros(cgcent) = v_cnpjemitente;
        END IF;

        IF v_codparceiro IS NULL
        THEN
          importar_participante_xml_nfe(v_codparceiro);
        END IF;
      END IF;

      IF v_codparceiro IS NULL
      THEN
        p_retorno := 'Parceiro ' || v_cnpjemitente || ' não cadastrado.';
        INSERT INTO arquivosxmlproc (
          data, nome, conteudo, retorno
        )
        SELECT data,
               nome,
               conteudo,
               p_retorno
          FROM arquivosxml;
        COMMIT;

        RETURN;
      END IF;

      NFENTRADA.CODFILIAL := V_CODFILIAL;
      NFENTRADA.CODFORNEC := V_CODPARCEIRO;
      NFENTRADA.CODCONT := CONSUM.CODCONTFOR;

      IF v_ufdestinatario = v_ufemitente
      THEN
        nfentrada.CODFISCAL := 111;
      ELSE
        nfentrada.codfiscal := 211;
      END IF;

      mov.codoper := 'E';
      mov.dtmov := TRUNC(nfentrada.dtent);
      mov.dtmovlog := mov.dtmov;
      mov.numnota := nfentrada.numnota;
      mov.codfilial := nfentrada.codfilial;

      INSERT INTO PCNFENT
      VALUES NFENTRADA;

      IF nfentrada.tipodescarga = '6'
      THEN

        CREDITO.CODCLI := V_CODPARCEIRO;
        CREDITO.DTLANC := TRUNC(SYSDATE);
        CREDITO.CODFILIAL := V_CODFILIAL;
        CREDITO.VALOR := NFENTRADA.VLTOTAL;
        CREDITO.CODFUNC := 1;
        CREDITO.HISTORICO := 'CREDITO DEVOLUCAO ' || TO_CHAR(NFENTRADA.NUMNOTA);
        CREDITO.CODFUNCLANC := 1;
        CREDITO.NUMERARIO := 'N';
        CREDITO.CODMOVIMENTO := 250004;
        CREDITO.CODROTINA := 618;
        CREDITO.NUMTRANS := NFENTRADA.NUMTRANSENT;

        INSERT INTO PCCRECLI
        VALUES credito;

      END IF;

    END IF;

    MOV.STATUS              := 'AB';
    MOV.NUMTRANSENT         := NFENTRADA.NUMTRANSENT;
    MOV.NUMTRANSVENDA       := NFSAIDA.NUMTRANSVENDA;
    MOV.CODCLI              := NFSAIDA.CODCLI;
    MOV.CODFORNEC           := NFENTRADA.CODFORNEC;
    MOV.PERCDESC            := 0;
    MOV.VLBONIFIC           := 0;
    MOV.CODFILIALRETIRA     := NFSAIDA.CODFILIAL;
    MOV.GERAICMSLIVROFISCAL := 'S';
    MOV.COMPRACONSIGNADO    := 'N';
    MOV.MOVESTOQUECONTABIL  := 'S';
    MOV.MOVESTOQUEGERENCIAL := 'S';
    MOV.NUMCAR              := NFSAIDA.NUMCAR;

    SELECT CONTEUDO
      INTO XMLNFE
      FROM arquivosxml;

    FOR reg IN (
    SELECT item.EXTRACT('/det/@nItem', v_xmlns).getstringval()
                       nitem,
                       item.EXTRACT('/det/prod/cProd/text()',
                       v_xmlns).getstringval()
                       cprod,
                       item.EXTRACT('/det/prod/cEAN/text()',
                       v_xmlns).getstringval()
                       cean,
                       item.EXTRACT('/det/prod/xProd/text()',
                       v_xmlns).getstringval()
                       xprod,
                       item.EXTRACT('/det/prod/NCM/text()',
                       v_xmlns).getstringval()
                       ncm,
                       item.EXTRACT('/det/prod/CFOP/text()',
                       v_xmlns).getnumberval()
                       cfop,
                       item.EXTRACT('/det/prod/uCom/text()',
                       v_xmlns).getstringval()
                       ucom,
                       item.EXTRACT('/det/prod/qCom/text()',
                       v_xmlns).getstringval()
                       qcom,
                       item.EXTRACT('/det/prod/vUnCom/text()',
                       v_xmlns).getstringval()
                       vuncom,
                       item.EXTRACT('/det/prod/vDesc/text()',
                       v_xmlns).getstringval()
                       vdesc,
                       item.EXTRACT('/det/prod/vFrete/text()',
                       v_xmlns).getstringval()
                       vfrete,

                       item.EXTRACT('/det/imposto/ICMSUFDest/vBCUFDest/text()',
                       v_xmlns).getstringval()
                       vbcufdest,

                       item.EXTRACT('/det/imposto/ICMSUFDest/vICMSUFRemet/text()',
                       'xmlns="http://www.portalfiscal.inf.br/nfe"').getstringval()
                       vICMSUFRemet,

                       item.EXTRACT('/det/imposto/ICMSUFDest/pICMSUFDest/text()',
                       v_xmlns).getstringval()
                       picmsufdest,
                       item.EXTRACT('/det/imposto/ICMSUFDest/pICMSInter/text()',
                       v_xmlns).getstringval()
                       picmsinter,
                       item.EXTRACT('/det/imposto/ICMSUFDest/pICMSInterPart/text()',
                       v_xmlns).getstringval()
                       picmsinterpart,
                       item.EXTRACT('/det/imposto/ICMSUFDest/vICMSUFDest/text()',
                       v_xmlns).getstringval()
                       vicmsufdest,
                       item.EXTRACT('/det/imposto/ICMSUFDest/pFCPUFDest/text()',
                       v_xmlns).getstringval()
                       pfcpufdest,
                       item.EXTRACT('/det/imposto/ICMSUFDest/vFCPUFDest/text()',
                       v_xmlns).getstringval()
                       vfcpufdest,

                       item.EXTRACT('/det/imposto/ICMSUFDest/vOutro/text()',
                       v_xmlns).getstringval()
                       voutro,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'orig')
                       orig,
                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'CST')
                       cst,
                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'pICMS')
                       picms,
                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vBC')
                       vbc,
                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vICMS')
                       vicms,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vBCSTRet')
                       vBCSTRet,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vICMSSTRet')
                       vICMSSTRet,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'pST')
                       pST,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vICMSSubstituto')
                       vICMSSubstituto,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vBCST')
                       vBCST,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'pICMSST')
                       pICMSST,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'pMVAST')
                       pMVAST,

                       obter_dados_icms_nfe(
                       item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                       'vICMSST')
                       vICMSST,

                       obter_dados_pis_nfe(
                       item.EXTRACT('/det/imposto/PIS', v_xmlns),
                       'pPIS')
                       vPPIS,

                       obter_dados_pis_nfe(
                       item.EXTRACT('/det/imposto/PIS', v_xmlns),
                       'vBC')
                       vBCPIS,

                       obter_dados_pis_nfe(
                       item.EXTRACT('/det/imposto/PIS', v_xmlns),
                       'vPIS')
                       vVPIS,

                       obter_dados_cofins_nfe(
                       item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                       'vBC')
                       vBCCOFINS,

                       obter_dados_cofins_nfe(
                       item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                       'pCOFINS')
                       vPCOFINS,

                       obter_dados_cofins_nfe(
                       item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                       'vCOFINS')
                       vVCOFINS,

                       obter_dados_ipi_nfe(
                       item.EXTRACT('/det/imposto/IPI', v_xmlns),
                       'vIPI')
                       vVIPI,

                       obter_dados_ipi_nfe(
                       item.EXTRACT('/det/imposto/IPI', v_xmlns),
                       'pIPI')
                       vPIPI,

                       obter_dados_ipi_nfe(
                       item.EXTRACT('/det/imposto/IPI', v_xmlns),
                       'vBC')
                       vBCIPI,

                       obter_dados_ipidev_nfe(
                       item.EXTRACT('/det/impostoDevol/IPI', v_xmlns),
                       'vIPIDevol')
                       vVIPIDev

        FROM TABLE (XMLSEQUENCE(EXTRACT(xmltype(xmlnfe),
        'nfeProc/NFe/infNFe/det',
        v_xmlns))) item)
    LOOP
      mov.numseq := reg.nitem;
      IF v_entrada_saida = 'saida'
      THEN
        mov.codfiscal := reg.cfop;
        mov.codprod := reg.cprod;

      ELSE --entrada
        mov.codprod := reg.cprod;

        dbms_output.put_line('Produto ' || mov.codprod);

        SELECT COUNT(*)
          INTO i
          FROM mscfop
          WHERE cfop_saida = reg.cfop;
        IF i = 1
        THEN
          SELECT x.cfop_entrada
            INTO mov.codfiscal
            FROM mscfop x
            WHERE cfop_saida = reg.cfop;
        ELSE
          mov.codfiscal := reg.cfop;
        END IF;
      END IF;

      -- VALIDAÇAO CODFAB PARA CODPROD
      --select count(*) into i from pcprodut where codfab=reg.cprod;

      --if i=1 then
      --  select codprod into mov.codprod from pcprodut where codfab=reg.cprod;
      --end if;
      -- fim

      IF reg.cean <> 'SEM GTIN'
      THEN
        mov.codauxiliar := apenasnumeros(reg.cean);
      ELSE
        mov.codauxiliar := NULL;
      END IF;

      mov.numtransitem := DFSEQ_PCMOVCOMPLE.NEXTVAL;

      mov.nbm := reg.ncm;

      mov.UNIDADE := SUBSTR(reg.ucom, 1, 2);

      mov.qt := numero(reg.qcom);

      mov.PUNIT := numero(reg.vuncom);

      mov.QTCONT := mov.qt;

      MOV.PUNITCONT := MOV.PUNIT;
      MOV.DESCRICAO := SUBSTR(REG.XPROD, 1, 40);
      MOV.VLDESCONTO := NVL(NUMERO(REG.VDESC), 0) / MOV.QT;
      MOV.PERCICM := NVL(NUMERO(REG.PICMS), 0);
      MOV.SITTRIBUT := REG.CST;
      MOV.VLFRETE := NUMERO(NVL(REG.VFRETE, 0)) / MOV.QT;
      MOV.BASEICMS := (NVL(NUMERO(REG.VBC) / MOV.QT, 0)) - NVL(MOV.VLFRETE, 0);
      MOV.BASEICMSBCR := MOV.PUNITCONT;

      MOV.VLOUTRASDESP := NUMERO(REG.VOUTRO) / MOV.QT;
      /* precisa de analise */
      MOV.BASEBCR := NUMERO(REG.VBCSTRET) / MOV.QT;
      MOV.STBCR := NUMERO(REG.VICMSSTRET) / MOV.QT;
      MOV.VLICMSBCR := NUMERO(REG.VICMSSUBSTITUTO) / MOV.QT;

      MOV.BASEICST := NUMERO(REG.VBCST) / MOV.QT;
      MOV.ST := NUMERO(REG.VICMSST) / MOV.QT;
      MOV.PERCST := NUMERO(REG.PICMSST);
      MOV.IVA := NUMERO(REG.PMVAST);

      MOV.PERPIS := NVL(REG.VPPIS, 0);
      MOV.VLPIS := NVL(REG.VVPIS / MOV.QT, 0);
      MOV.VLBASEPISCOFINS := NVL(REG.VBCPIS, 0);

      MOV.PERCOFINS := NVL(REG.VPCOFINS, 0);
      MOV.VLCOFINS := NVL(REG.VVCOFINS / MOV.QT, 0);

      MOV.VLIPI := REG.VVIPI / MOV.QT;
      MOV.PERCIPI := REG.VPIPI;
      MOV.VLBASEIPI := REG.VBCIPI / MOV.QT;

      IF (v_entrada_saida = 'entrada')
        AND (NVL(reg.vVIPIDev, 0) > 0)
      THEN
        mov.vlipi := reg.vVIPIDev / mov.qt;
        mov.percipi := ROUND(((reg.vVIPIDev / mov.qt) / (mov.punit * mov.qtcont) * 100), 2);
        mov.vlbaseipi := (mov.punit * mov.qtcont);
      END IF;

      IF mov.st > 0
      THEN
        mov.punitcont := mov.punit + mov.st;
      END IF;

      IF mov.vlipi > 0
      THEN
        mov.punitcont := mov.punitcont + mov.vlipi;
      END IF;

      mov.NUMLOTE := '1';

      IF v_entrada_saida = 'saida'
        AND LOWER(v_natop) LIKE '%venda%'
      THEN

        -- consultando ultima data com registro na pchistest
        SELECT MAX(data)
          INTO v_dtesthist
          FROM pchistest
          WHERE pchistest.codfilial = nfsaida.codfilial
            AND pchistest.codprod = reg.cprod
            AND pchistest.data < TRUNC(nfsaida.dtsaida);


        -- consultando custos na tabela pchistest
        SELECT HIST.CUSTOFIN,
               HIST.CUSTOREAL,
               HIST.CUSTOREP,
               HIST.CUSTOCONT
          INTO V_CUSTOFIN,
               V_CUSTOREAL,
               V_CUSTOREP,
               V_CUSTOCONT
          FROM PCEST HIST
          WHERE HIST.CODPROD = REG.CPROD                -- CODPROD
            AND HIST.CODFILIAL = NFSAIDA.CODFILIAL;     -- FILIAL DA MOVIMENTACAO
      END IF;

      -- consultando parametro de tributacao por estado
      v_tribuf := consum.tributafreterateado;

      IF v_entrada_saida = 'saida'
        AND v_tribuf = 'S'
      THEN
        -- consultando CODST e CODICMTAB do produto
        SELECT CODST,
               CODICMTAB,
               CODICMTABPF
          INTO V_CODST,
               V_CMV,
               V_CMVPF
          FROM PCTRIBUT
          WHERE CODST IN (SELECT CODST
                FROM PCTABTRIB
                WHERE CODPROD = MOV.CODPROD
                  AND UFDESTINO = V_UFDESTINATARIO);
      END IF;

      /*
      -- gravando %cmv do produto
      IF length(v_cnpjdestinatrio) = 14
          THEN
              mov.codicmtab := v_cmv;    -- CMV PESSOA JURIDICA
          ELSE
              mov.codicmtab := v_cmvpf;  -- CMV PESSOA FISICA
      END IF;
      */

      -- GRAVANDO %CMV DO PRODUTO
      MOV.CODICMTAB := V_CMVPF;
      MOV.CODST := V_CODST;

      -- GRAVANDO CUSTOS PCMOV
      MOV.CUSTOFIN := ((NVL(MOV.CODICMTAB, 0) / 100) * MOV.PUNIT) + V_CUSTOFIN;
      MOV.CUSTOREAL := ((NVL(MOV.CODICMTAB, 0) / 100) * MOV.PUNIT) + V_CUSTOREAL;
      MOV.CUSTOREP := V_CUSTOREP;
      MOV.CUSTOCONT := V_CUSTOCONT;

      MOV.CUSTOFINEST := V_CUSTOFIN;

      -- GRAVANDO DADOS MOVCOMPLE
      MOVCOMPLE.NUMTRANSITEM := DFSEQ_PCMOVCOMPLE.CURRVAL;
      MOVCOMPLE.DTREGISTRO := MOV.DTMOV;
      MOVCOMPLE.ALIQICMS1RET := NUMERO(REG.PST);

      -- DIFAL
      MOVCOMPLE.VLBASEPARTDEST := NUMERO(REG.VBCUFDEST);             -- VL.BASE CALC.UF DESTINO
      MOVCOMPLE.ALIQINTERNADEST := NUMERO(REG.PICMSUFDEST);          -- % ICMS UF DESTINO
      MOVCOMPLE.ALIQINTERORIGPART := NUMERO(REG.PICMSINTER);         -- % ICMS INTER. ORIGEM
      MOVCOMPLE.PERCPROVPART := NUMERO(REG.PICMSINTERPART);          -- % PROV. ICMS PARTILHA
      MOVCOMPLE.VLICMSPARTDEST := NUMERO(REG.VICMSUFDEST) / MOV.QT;  -- VL.ICMS UF DESTINO
      MOVCOMPLE.VLICMSPARTREM := NVL(NUMERO(REG.VICMSUFREMET) / MOV.QT, 0);

      -- FCP
      MOVCOMPLE.ALIQFCP := NUMERO(REG.PFCPUFDEST);                   -- %
      MOVCOMPLE.VLFCPPART := NUMERO(REG.VFCPUFDEST) / MOV.QT;        -- VALOR


      IF v_tpnf = 1
        AND NVL(mov.baseicms, 0) > 0
      THEN    -- A gravacao na movimentacao de saida é gravado unitariamente
        mov.baseicms := mov.baseicms / mov.qt;
      END IF;

      SELECT COUNT(*)
        INTO i
        FROM pcprodut
        WHERE codprod = mov.codprod;

      IF i = 0
      THEN
        p_retorno := 'Produto XML ' || reg.cprod || ' Não cadastrado, Filial: ' || v_codfilial || ' Tipo: ' || v_entrada_saida || ' EAN: ' || reg.cEAN;
        ROLLBACK;
        INSERT INTO arquivosxmlproc (
          data, nome, conteudo, retorno
        )
        SELECT data,
               nome,
               conteudo,
               p_retorno
          FROM arquivosxml;
        COMMIT;

        RETURN;
      END IF;

      INSERT INTO pcmov
      VALUES mov;

      INSERT INTO pcmovcomple
      VALUES movcomple;

    END LOOP;

    dbms_output.put_line('Incluindo custos na tabela pcnfsaid.... ');

    IF v_entrada_saida = 'saida'
    THEN

      SELECT ROUND(SUM(custofin * qt), 2),
             ROUND(SUM(custoreal * qt), 2),
             ROUND(SUM(custorep * qt), 2),
             ROUND(SUM(custocont * qt), 2)
        INTO v_custofin,
             v_custoreal,
             v_custorep,
             v_custocont
        FROM pcmov
        WHERE pcmov.numtransvenda = nfsaida.numtransvenda;


      UPDATE PCNFSAID
        SET VLCUSTOFIN = V_CUSTOFIN, VLCUSTOREAL = V_CUSTOREAL, VLCUSTOREP = V_CUSTOREP, VLCUSTOCONT = V_CUSTOCONT
        WHERE PCNFSAID.NUMTRANSVENDA = NFSAIDA.NUMTRANSVENDA;


      COMMIT;

    END IF;

    dbms_output.put_line('Incluindo pcnfbase .... ');

    -- PCNFBASE
    FOR reg
    IN (SELECT cfop,
               SUM(vbc) baseicms,
               SUM(vicms) vlicms,
               AVG(picms) aliquota
        FROM (
        SELECT item.EXTRACT('/det/@nItem', v_xmlns).getstringval()
                     nitem,
                     item.EXTRACT('/det/prod/CFOP/text()',
                     v_xmlns).getnumberval()
                     cfop,
                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'orig')
                     orig,
                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'CST')
                     cst,
                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'pICMS')
                     picms,
                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vBC')
                     vbc,
                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vICMS')
                     vicms,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vBCSTRet')
                     vBCSTRet,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vICMSSTRet')
                     vICMSSTRet,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'pST')
                     pST,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vICMSSubstituto')
                     vICMSSubstituto,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vBCST')
                     vBCST,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'pICMSST')
                     pICMSST,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'pMVAST')
                     pMVAST,

                     obter_dados_icms_nfe(
                     item.EXTRACT('/det/imposto/ICMS', v_xmlns),
                     'vICMSST')
                     vICMSST,

                     obter_dados_pis_nfe(
                     item.EXTRACT('/det/imposto/PIS', v_xmlns),
                     'pPIS')
                     vPPIS,

                     obter_dados_pis_nfe(
                     item.EXTRACT('/det/imposto/PIS', v_xmlns),
                     'vBC')
                     vBCPIS,

                     obter_dados_pis_nfe(
                     item.EXTRACT('/det/imposto/PIS', v_xmlns),
                     'vPIS')
                     vVPIS,

                     obter_dados_cofins_nfe(
                     item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                     'vBC')
                     vBCCOFINS,

                     obter_dados_cofins_nfe(
                     item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                     'pCOFINS')
                     vPCOFINS,

                     obter_dados_cofins_nfe(
                     item.EXTRACT('/det/imposto/COFINS', v_xmlns),
                     'vCOFINS')
                     vVCOFINS,

                     obter_dados_ipi_nfe(
                     item.EXTRACT('/det/imposto/IPI', v_xmlns),
                     'vIPI')
                     vVIPI,

                     obter_dados_ipi_nfe(
                     item.EXTRACT('/det/imposto/IPI', v_xmlns),
                     'pIPI')
                     vPIPI,

                     obter_dados_ipi_nfe(
                     item.EXTRACT('/det/imposto/IPI', v_xmlns),
                     'vBC')
                     vBCIPI

              FROM TABLE (XMLSEQUENCE(EXTRACT(xmltype((SELECT conteudo
                  FROM arquivosxml)),
              'nfeProc/NFe/infNFe/det',
              v_xmlns))) item)
        GROUP BY cfop)
    LOOP
      INSERT INTO PCNFBASE (
        NUMTRANSVENDA, NUMTRANSENT, CODCONT, CODFISCAL, VLBASE, VLICMS, ALIQUOTA
      )
      VALUES (NFSAIDA.NUMTRANSVENDA, NFENTRADA.NUMTRANSENT, NVL(NFSAIDA.CODCONT, NFENTRADA.CODCONT), REG.CFOP, NVL(REG.BASEICMS, 0), NVL(REG.VLICMS, 0), NVL(REG.ALIQUOTA, 0));
    END LOOP;

    IF 1 = 1
    THEN
      dbms_output.put_line('Atualizar Saidas do Estoque.... ');


      IF nfsaida.numtransvenda > 0
      THEN
        FOR reg
        IN (SELECT SUM(qtcont) qtde,
                   codprod,
                   codfilial
            FROM pcmov
            WHERE numtransvenda = nfsaida.numtransvenda
              AND dtcancel IS NULL
              AND qtcont > 0
            GROUP BY codprod,
                     codfilial)
        LOOP
          UPDATE PCEST
            SET QTEST = NVL(QTEST, 0) - REG.QTDE, QTESTGER = NVL(QTESTGER, 0) - REG.QTDE
            WHERE CODPROD = REG.CODPROD
            AND CODFILIAL = REG.CODFILIAL;

          UPDATE PCMOV
            SET DTMOVLOG = DTMOV
            WHERE CODPROD = REG.CODPROD
            AND NUMTRANSVENDA = NFSAIDA.NUMTRANSVENDA;
        END LOOP;

      END IF;

      dbms_output.put_line('--Atualizar Entradas no Estoque');

      IF nfentrada.numtransent > 0
      THEN
        FOR reg
        IN (SELECT SUM(qtcont) qtde,
                   codprod,
                   codfilial
            FROM pcmov
            WHERE numtransent = nfentrada.numtransent
              AND dtcancel IS NULL
              AND qtcont > 0
            GROUP BY codprod,
                     codfilial)
        LOOP
          UPDATE PCEST
            SET QTEST     = NVL(QTEST, 0) + REG.QTDE, 
                QTESTGER  = NVL(QTESTGER, 0) + REG.QTDE
          WHERE CODPROD   = REG.CODPROD
            AND CODFILIAL = REG.CODFILIAL;

          UPDATE PCLOTE
            SET QT = NVL(QT, 0) + REG.QTDE
            WHERE CODFILIAL = REG.CODFILIAL
            AND CODPROD = REG.CODPROD
            AND NUMLOTE = '1';

          UPDATE PCMOV
            SET DTMOVLOG = DTMOV
            WHERE CODPROD = REG.CODPROD
            AND NUMTRANSENT = NFENTRADA.NUMTRANSENT;

        END LOOP;
      END IF;
    END IF;
    dbms_output.put_line('--Concluido');

    p_retorno := 'ok';
    INSERT INTO arquivosxmlproc (
      data, nome, conteudo, retorno
    )
    SELECT data,
           nome,
           conteudo,
           'Importado com sucesso Transação: ' || nfsaida.numtransvenda || ' ' || nfentrada.numtransent
      FROM arquivosxml;
    COMMIT;

  END;
/

COMMIT;

-- =====================================================
-- PROCEDURE NERVSFLOW_INTEGRADORA
-- Criada para integração com N8N - NERVS FLOW
-- Recebe dados extraídos do XML como parâmetros
-- =====================================================


-- =====================================================
-- PROCEDURE PRINCIPAL NERVSFLOW_INTEGRADORA
-- =====================================================
CREATE OR REPLACE PROCEDURE nervsflow_integradora(
  -- Parâmetros de identificação da NFe
  p_chavenfe            IN VARCHAR2,
  p_serie               IN VARCHAR2,
  p_numnota             IN NUMBER,
  p_tpnf                IN NUMBER,
  p_finnfe              IN NUMBER,
  p_tpimp               IN NUMBER,
  p_natop               IN VARCHAR2,
  p_dtemissao           IN DATE,

  -- Dados do emitente
  p_cnpjemitente        IN VARCHAR2,
  p_ufemitente          IN VARCHAR2,

  -- Dados do destinatário
  p_cnpjdestinatario    IN VARCHAR2,
  p_ufdestinatario      IN VARCHAR2,
  p_enderdestinatario   IN VARCHAR2,
  p_ender_numero        IN VARCHAR2,
  p_bairro_destinatario IN VARCHAR2,
  p_xmun                IN VARCHAR2,
  p_xpais               IN VARCHAR2,
  p_cpais               IN VARCHAR2,
  p_cmun                IN VARCHAR2,
  p_cep                 IN VARCHAR2,

  -- Totais da NFe
  p_vltotal             IN NUMBER,
  p_vlfrete             IN NUMBER,
  p_vloutros            IN NUMBER,
  p_vst                 IN NUMBER,
  p_vbcst               IN NUMBER,
  p_vipi                IN NUMBER,
  p_vipidev             IN NUMBER,
  p_vcofins             IN NUMBER,
  p_vpis                IN NUMBER,

  -- Array de itens
  p_itens               IN t_itens_nfe,

  -- Parâmetro de retorno
  p_retorno             OUT VARCHAR2
) IS
  -- Variáveis locais (mantendo as mesmas da procedure original)
  i                     INTEGER;
  v_numtrans            INTEGER;
  v_codparceiro         INTEGER;
  v_codfilial           VARCHAR2(2);
  v_entrada_saida       VARCHAR2(10);

  v_custofin            NUMBER;
  v_custoreal           NUMBER;
  v_custorep            NUMBER;
  v_custocont           NUMBER;
  v_dtesthist           DATE;

  v_cmv                 NUMBER;
  v_cmvpf               NUMBER;
  v_codst               INTEGER;
  v_tribuf              VARCHAR2(1);

  nfsaida               pcnfsaid%ROWTYPE;
  nfentrada             pcnfent%ROWTYPE;
  credito               pccrecli%ROWTYPE;
  mov                   pcmov%ROWTYPE;
  movcomple             pcmovcomple%ROWTYPE;
  prest                 pcprest%ROWTYPE;
  consum                pcconsum%ROWTYPE;

  v_msg                 VARCHAR2(5000);

BEGIN

  dbms_output.put_line('Iniciando NERVSFLOW_INTEGRADORA....');

  -- Buscar configurações do sistema
  SELECT *
    INTO consum
    FROM pcconsum;

  dbms_output.put_line('Chave: ' || p_chavenfe);
  dbms_output.put_line('Total: ' || p_vltotal);
  dbms_output.put_line('CNPJ Emitente: ' || p_cnpjemitente);
rg
  -- Determinar filial e tipo de operação baseado nos CNPJs
  v_codfilial := '';
  v_entrada_saida := '';

  IF p_cnpjdestinatario = '05330305000690' THEN
    v_codfilial := '6';
    v_entrada_saida := 'entrada';
  ELSE
    IF p_cnpjemitente = '05330305000690' THEN
      v_codfilial := '6';
      v_entrada_saida := 'saida';

      IF (LOWER(p_natop) = 'outras entradas - retorno simbolico de deposito temporario')
        OR (p_tpnf = 0) THEN
        v_entrada_saida := 'entrada';
      END IF;
    END IF;
  END IF;

  IF (v_codfilial = '') OR (v_codfilial IS NULL) THEN
    IF p_cnpjdestinatario = '05330305000185' THEN
      v_codfilial := '1';
      v_entrada_saida := 'entrada';
    ELSE
      IF p_cnpjemitente = '05330305000185' THEN
        v_codfilial := '1';
        v_entrada_saida := 'saida';

        IF (LOWER(p_natop) = 'outras entradas - retorno simbolico de deposito temporario')
          OR (p_tpnf = 0) THEN
          v_entrada_saida := 'entrada';
        END IF;
      END IF;
    END IF;
  END IF;

  IF v_codfilial = '' THEN
    p_retorno := 'XML NF-e não autorizada (' || p_chavenfe || ')';
    RETURN;
  END IF;

  dbms_output.put_line('Filial: ' || v_codfilial);

  -- Verificar se a NFe já foi processada
  IF v_entrada_saida = 'saida' THEN
    SELECT COUNT(*), MAX(numtransvenda)
      INTO i, v_numtrans
      FROM pcnfsaid
      WHERE chavenfe = p_chavenfe
        AND dtcancel IS NULL
        AND codfilial = v_codfilial;

    IF i > 0 THEN
      p_retorno := 'XML saida NF-e ja processado anteriormente (' || p_chavenfe || ', Num.Trans.Venda:' || v_numtrans || ')';
      RETURN;
    END IF;
  END IF;

  SELECT COUNT(*), MAX(numtransent)
    INTO i, v_numtrans
    FROM pcnfent
    WHERE chavenfe = p_chavenfe
      AND dtcancel IS NULL;

  IF i > 0 THEN
    p_retorno := 'XML entrada NF-e ja processado anteriormente(' || p_chavenfe || ', transacao:' || v_numtrans || ')';
    RETURN;
  END IF;

  IF UPPER(p_natop) LIKE '%ZZZZZ%' THEN
    p_retorno := 'Nota fiscal de servico';
    RETURN;
  END IF;

  -- =====================================================
  -- PROCESSAMENTO DE NOTA FISCAL DE SAÍDA
  -- =====================================================
  IF v_entrada_saida = 'saida' THEN
    -- Dados da Nota Fiscal de Saida
    nfsaida.ESPECIE := 'NF';

    SELECT proxnumtransvenda
      INTO nfsaida.numtransvenda
      FROM pcconsum
    FOR UPDATE NOWAIT;

    UPDATE PCCONSUM
      SET PROXNUMTRANSVENDA = PROXNUMTRANSVENDA + 1;

    COMMIT;

    nfsaida.tipoemissao   := p_tpimp;
    nfsaida.finalidadenfe := p_finnfe;
    nfsaida.caixa         := 0;
    nfsaida.serie         := p_serie;
    nfsaida.numnota       := p_numnota;
    nfsaida.vlfrete       := NVL(p_vlfrete, 0);
    nfsaida.vloutrasdesp  := NVL(p_vloutros, 0);
    nfsaida.dtsaida       := TRUNC(p_dtemissao);
    nfsaida.dtentrega     := TRUNC(nfsaida.dtsaida);
    nfsaida.vltotal       := p_vltotal;
    nfsaida.icmsretido    := NVL(p_vst, 0);
    nfsaida.bcst          := NVL(p_vbcst, 0);
    nfsaida.vlipi         := NVL(p_vipi, 0);
    nfsaida.vlcofins      := NVL(p_vcofins, 0);
    nfsaida.vlpis         := NVL(p_vpis, 0);
    nfsaida.codpais       := NVL(TO_NUMBER(p_cpais), 0);
    nfsaida.descpais      := p_xpais;
    nfsaida.cep           := REGEXP_REPLACE(p_cep, '[^0-9]', '');
    nfsaida.uf            := p_ufdestinatario;

    -- NOTAS DE REMESSA NAO DEVEM APARECER NA ROTINA 111
    IF LOWER(p_natop) LIKE '%venda%' THEN
      nfsaida.tipovenda := 1;
    ELSE
      nfsaida.tipovenda := 'SR';
    END IF;

    SELECT NVL(MAX(codcidade), 0)
      INTO nfsaida.codmunicipio
      FROM pccidade
      WHERE codibge = p_cmun;

    NFSAIDA.MUNICIPIO := SUBSTR(p_xmun, 1, 15);
    NFSAIDA.ENDERECO := SUBSTR(p_enderdestinatario, 1, 40);
    NFSAIDA.NUMENDERECO := SUBSTR(p_ender_numero, 1, 5);
    NFSAIDA.BAIRRO := SUBSTR(p_bairro_destinatario, 1, 20);

    NFSAIDA.VLTOTGER := NFSAIDA.VLTOTAL;
    NFSAIDA.CHAVENFE := p_chavenfe;
    NFSAIDA.PRAZOPONDERADO := 'N';
    NFSAIDA.PERBASEREDOUTRASDESP := 0;
    NFSAIDA.GERACP := 'N';
    NFSAIDA.CODFISCALFRETE := 0;
    NFSAIDA.PERCICMFRETE := 0;
    NFSAIDA.AMBIENTENFE := 'P';
    NFSAIDA.CONFERIDO := 'N';
    NFSAIDA.AGREGASTVLMERC := 'N';
    NFSAIDA.EMISSNUMAUTOMATICO := 'N';
    NFSAIDA.CTEREGIMEESPECIAL := 'N';
    NFSAIDA.NFIPIEMITIDA := 'N';
    NFSAIDA.REDUZICMSDOCTE := 'N';
    NFSAIDA.GERARBCRNFE := 'S';
    NFSAIDA.ALIQICMOUTRASDESP := 0;
    NFSAIDA.CODFISCALOUTRASDESP := 0;
    NFSAIDA.CODSITTRIBPISCOFINS := 1;
    NFSAIDA.CODFISCALNF := NULL;
    NFSAIDA.DTLANCTO := TRUNC(SYSDATE);
    NFSAIDA.VLBASEIPI := 0;
    NFSAIDA.PERCOFINS := 0;
    NFSAIDA.PERPIS := 0;
    NFSAIDA.VLBASEPISCOFINS := 0;
    NFSAIDA.CODCOB := 'CARC';
    NFSAIDA.CODPLPAG := 1;
    NFSAIDA.CONDVENDA := 1;
    NFSAIDA.NUMCAR := 0;

    SELECT MAX(codcli)
      INTO v_codparceiro
      FROM pcclient
      WHERE REGEXP_REPLACE(cgcent, '[^0-9]', '') = p_cnpjdestinatario;

    IF v_codparceiro IS NULL THEN
      -- Aqui seria necessário implementar a criação do cliente
      -- Por enquanto, retornamos erro
      p_retorno := 'Cliente ' || p_cnpjdestinatario || ' não cadastrado.';
      RETURN;
    END IF;

    NFSAIDA.CODCLI := v_codparceiro;
    NFSAIDA.CODCLINF := v_codparceiro;
    NFSAIDA.CODFILIAL := v_codfilial;
    NFSAIDA.CODFILIALNF := v_codfilial;
    NFSAIDA.MUNICIPIO := SUBSTR(p_xmun, 1, 30);

    IF p_finnfe = 4 THEN
      -- Devolucao de Fornecedor
      nfsaida.codcont := consum.codcontfor;

      IF p_ufdestinatario = p_ufemitente THEN
        nfsaida.codfiscal := 521;
      ELSE
        nfsaida.codfiscal := 621;
      END IF;
    ELSE
      -- Demais Notas de Saida
      DBMS_OUTPUT.put_line('Cod.Parceiro: ' || v_codparceiro);

      SELECT codusur1, codpraca
        INTO nfsaida.codusur, nfsaida.codpraca
        FROM pcclient
        WHERE codcli = v_codparceiro;

      nfsaida.CODCONT := consum.codcontcli;

      IF p_ufdestinatario = p_ufemitente THEN
        nfsaida.codfiscal := 512;
      ELSE
        nfsaida.codfiscal := 612;
      END IF;
    END IF;

    nfsaida.codfiscal := 76;
    nfsaida.codusur := 802;

    SELECT NVL(MAX(codsupervisor), 0)
      INTO nfsaida.codsupervisor
      FROM pcusuari
      WHERE codusur = nfsaida.codusur;

    nfsaida.codsupervisor := nfsaida.codsupervisor;

    MOV.CODOPER := 'S';
    MOV.DTMOV := TRUNC(NFSAIDA.DTSAIDA);
    MOV.DTMOVLOG := MOV.DTMOV;
    MOV.NUMTRANSVENDA := NFSAIDA.NUMTRANSVENDA;
    MOV.NUMNOTA := NFSAIDA.NUMNOTA;
    MOV.CODFILIAL := NFSAIDA.CODFILIAL;
    MOV.CODCLI := v_codparceiro;

    INSERT INTO pcnfsaid VALUES nfsaida;

    IF LOWER(p_natop) LIKE '%venda%' THEN
      PREST.NUMTRANSVENDA := MOV.NUMTRANSVENDA;
      PREST.VALOR := NFSAIDA.VLTOTAL;
      PREST.PREST := '1';
      PREST.DUPLIC := NFSAIDA.NUMNOTA;
      PREST.DTVENC := TRUNC(SYSDATE) + 30;
      PREST.DTVENCORIG := TRUNC(NFSAIDA.DTSAIDA) + 30;
      PREST.DTEMISSAO := TRUNC(NFSAIDA.DTSAIDA);
      PREST.CODCOB := 'CML';
      PREST.CODCOBORIG := 'CML';
      PREST.CODFILIAL := NFSAIDA.CODFILIAL;
      PREST.STATUS := 'A';
      PREST.VALORORIG := NFSAIDA.VLTOTAL;
      PREST.TXPERM := 0;
      PREST.OPERACAO := 'S';
      PREST.CODUSUR := NFSAIDA.CODUSUR;
      PREST.CODCLI := v_codparceiro;
      INSERT INTO pcprest VALUES prest;
    END IF;

  END IF;

  -- =====================================================
  -- PROCESSAMENTO DE NOTA FISCAL DE ENTRADA
  -- =====================================================
  IF v_entrada_saida = 'entrada' THEN
    -- Dados da Nota Fiscal de Entrada
    nfentrada.especie := 'NF';

    SELECT PROXNUMTRANSENT
      INTO nfentrada.numtransent
      FROM pcconsum
    FOR UPDATE NOWAIT;

    UPDATE PCCONSUM
      SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;

    COMMIT;

    NFENTRADA.DTEMISSAO := TRUNC(p_dtemissao);
    NFENTRADA.TIPODESCARGA := '2';
    NFENTRADA.SERIE := p_serie;
    NFENTRADA.NUMNOTA := p_numnota;
    NFENTRADA.VLFRETE := NVL(p_vlfrete, 0);
    NFENTRADA.DTENT := TRUNC(p_dtemissao);
    NFENTRADA.VLTOTAL := p_vltotal;
    NFENTRADA.VLST := NVL(p_vst, 0);
    NFENTRADA.BASEICST := NVL(p_vbcst, 0);
    NFENTRADA.VLIPI := NVL(p_vipi, 0);

    IF NVL(p_vipidev, 0) > 0 THEN
      nfentrada.vlipi := p_vipidev;
    END IF;

    nfentrada.chavenfe := p_chavenfe;

    SELECT MIN(codfornec)
      INTO v_codparceiro
      FROM pcfornec
      WHERE REGEXP_REPLACE(cgc, '[^0-9]', '') = p_cnpjemitente;

    IF p_finnfe = '4' THEN
      nfentrada.TIPODESCARGA := '6';

      IF (p_cnpjemitente = '05330305000185')
        OR (p_cnpjemitente = '05330305000690') THEN
        SELECT MAX(codcli)
          INTO v_codparceiro
          FROM pcclient
          WHERE REGEXP_REPLACE(cgcent, '[^0-9]', '') = p_cnpjdestinatario;
      ELSE
        SELECT MAX(codcli)
          INTO v_codparceiro
          FROM pcclient
          WHERE REGEXP_REPLACE(cgcent, '[^0-9]', '') = p_cnpjemitente;
      END IF;

      IF v_codparceiro IS NULL THEN
        -- Aqui seria necessário implementar a criação do cliente
        -- Por enquanto, retornamos erro
        p_retorno := 'Cliente para devolução ' || p_cnpjemitente || ' não cadastrado.';
        RETURN;
      END IF;
    END IF;

    IF v_codparceiro IS NULL THEN
      p_retorno := 'Fornecedor ' || p_cnpjemitente || ' não cadastrado.';
      RETURN;
    END IF;

    NFENTRADA.CODFILIAL := v_codfilial;
    NFENTRADA.CODFORNEC := v_codparceiro;
    NFENTRADA.CODCONT := CONSUM.CODCONTFOR;

    IF p_ufdestinatario = p_ufemitente THEN
      nfentrada.CODFISCAL := 111;
    ELSE
      nfentrada.codfiscal := 211;
    END IF;

    mov.codoper := 'E';
    mov.dtmov := TRUNC(nfentrada.dtent);
    mov.dtmovlog := mov.dtmov;
    mov.numnota := nfentrada.numnota;
    mov.codfilial := nfentrada.codfilial;

    INSERT INTO PCNFENT VALUES NFENTRADA;

    IF nfentrada.tipodescarga = '6' THEN
      CREDITO.CODCLI := v_codparceiro;
      CREDITO.DTLANC := TRUNC(SYSDATE);
      CREDITO.CODFILIAL := v_codfilial;
      CREDITO.VALOR := NFENTRADA.VLTOTAL;
      CREDITO.CODFUNC := 1;
      CREDITO.HISTORICO := 'CREDITO DEVOLUCAO ' || TO_CHAR(NFENTRADA.NUMNOTA);
      CREDITO.CODFUNCLANC := 1;
      CREDITO.NUMERARIO := 'N';
      CREDITO.CODMOVIMENTO := 250004;
      CREDITO.CODROTINA := 618;
      CREDITO.NUMTRANS := NFENTRADA.NUMTRANSENT;

      INSERT INTO PCCRECLI VALUES credito;
    END IF;

  END IF;

  -- =====================================================
  -- PROCESSAMENTO DOS ITENS DA NOTA FISCAL
  -- =====================================================

  -- Configurar dados comuns do movimento
  MOV.STATUS              := 'AB';
  MOV.NUMTRANSENT         := NFENTRADA.NUMTRANSENT;
  MOV.NUMTRANSVENDA       := NFSAIDA.NUMTRANSVENDA;
  MOV.CODCLI              := NFSAIDA.CODCLI;
  MOV.CODFORNEC           := NFENTRADA.CODFORNEC;
  MOV.PERCDESC            := 0;
  MOV.VLBONIFIC           := 0;
  MOV.CODFILIALRETIRA     := NFSAIDA.CODFILIAL;
  MOV.GERAICMSLIVROFISCAL := 'S';
  MOV.COMPRACONSIGNADO    := 'N';
  MOV.MOVESTOQUECONTABIL  := 'S';
  MOV.MOVESTOQUEGERENCIAL := 'S';
  MOV.NUMCAR              := NFSAIDA.NUMCAR;

  -- Processar cada item da nota fiscal
  FOR idx IN 1..p_itens.COUNT LOOP

    mov.numseq := p_itens(idx).nitem;

    IF v_entrada_saida = 'saida' THEN
      mov.codfiscal := p_itens(idx).cfop;
      mov.codprod := p_itens(idx).cprod;
    ELSE -- entrada
      mov.codprod := p_itens(idx).cprod;

      dbms_output.put_line('Produto ' || mov.codprod);

      SELECT COUNT(*)
        INTO i
        FROM mscfop
        WHERE cfop_saida = p_itens(idx).cfop;
      IF i = 1 THEN
        SELECT x.cfop_entrada
          INTO mov.codfiscal
          FROM mscfop x
          WHERE cfop_saida = p_itens(idx).cfop;
      ELSE
        mov.codfiscal := p_itens(idx).cfop;
      END IF;
    END IF;

    IF p_itens(idx).cean <> 'SEM GTIN' THEN
      mov.codauxiliar := REGEXP_REPLACE(p_itens(idx).cean, '[^0-9]', '');
    ELSE
      mov.codauxiliar := NULL;
    END IF;

    mov.numtransitem := DFSEQ_PCMOVCOMPLE.NEXTVAL;
    mov.nbm := p_itens(idx).ncm;
    mov.UNIDADE := SUBSTR(p_itens(idx).ucom, 1, 2);
    mov.qt := p_itens(idx).qcom;
    mov.PUNIT := p_itens(idx).vuncom;
    mov.QTCONT := mov.qt;
    MOV.PUNITCONT := MOV.PUNIT;
    MOV.DESCRICAO := SUBSTR(p_itens(idx).xprod, 1, 40);
    MOV.VLDESCONTO := NVL(p_itens(idx).vdesc, 0) / MOV.QT;
    MOV.PERCICM := NVL(p_itens(idx).picms, 0);
    MOV.SITTRIBUT := p_itens(idx).cst;
    MOV.VLFRETE := NVL(p_itens(idx).vfrete, 0) / MOV.QT;
    MOV.BASEICMS := (NVL(p_itens(idx).vbc / MOV.QT, 0)) - NVL(MOV.VLFRETE, 0);
    MOV.BASEICMSBCR := MOV.PUNITCONT;

    MOV.VLOUTRASDESP := NVL(p_itens(idx).voutro, 0) / MOV.QT;
    MOV.BASEBCR := NVL(p_itens(idx).vbcstret, 0) / MOV.QT;
    MOV.STBCR := NVL(p_itens(idx).vicmsstret, 0) / MOV.QT;
    MOV.VLICMSBCR := NVL(p_itens(idx).vicmssubstituto, 0) / MOV.QT;

    MOV.BASEICST := NVL(p_itens(idx).vbcst, 0) / MOV.QT;
    MOV.ST := NVL(p_itens(idx).vicmsst, 0) / MOV.QT;
    MOV.PERCST := NVL(p_itens(idx).picmsst, 0);
    MOV.IVA := NVL(p_itens(idx).pmvast, 0);

    MOV.PERPIS := NVL(p_itens(idx).ppis, 0);
    MOV.VLPIS := NVL(p_itens(idx).vpis / MOV.QT, 0);
    MOV.VLBASEPISCOFINS := NVL(p_itens(idx).vbcpis, 0);

    MOV.PERCOFINS := NVL(p_itens(idx).pcofins, 0);
    MOV.VLCOFINS := NVL(p_itens(idx).vcofins / MOV.QT, 0);

    MOV.VLIPI := NVL(p_itens(idx).vipi, 0) / MOV.QT;
    MOV.PERCIPI := NVL(p_itens(idx).pipi, 0);
    MOV.VLBASEIPI := NVL(p_itens(idx).vbcipi, 0) / MOV.QT;

    IF (v_entrada_saida = 'entrada') AND (NVL(p_itens(idx).vipidev, 0) > 0) THEN
      mov.vlipi := p_itens(idx).vipidev / mov.qt;
      mov.percipi := ROUND(((p_itens(idx).vipidev / mov.qt) / (mov.punit * mov.qtcont) * 100), 2);
      mov.vlbaseipi := (mov.punit * mov.qtcont);
    END IF;

    IF mov.st > 0 THEN
      mov.punitcont := mov.punit + mov.st;
    END IF;

    IF mov.vlipi > 0 THEN
      mov.punitcont := mov.punitcont + mov.vlipi;
    END IF;

    mov.NUMLOTE := '1';

    -- Cálculo de custos para saída
    IF v_entrada_saida = 'saida' AND LOWER(p_natop) LIKE '%venda%' THEN
      -- consultando ultima data com registro na pchistest
      SELECT MAX(data)
        INTO v_dtesthist
        FROM pchistest
        WHERE pchistest.codfilial = nfsaida.codfilial
          AND pchistest.codprod = p_itens(idx).cprod
          AND pchistest.data < TRUNC(nfsaida.dtsaida);

      -- consultando custos na tabela pcest
      SELECT HIST.CUSTOFIN, HIST.CUSTOREAL, HIST.CUSTOREP, HIST.CUSTOCONT
        INTO v_custofin, v_custoreal, v_custorep, v_custocont
        FROM PCEST HIST
        WHERE HIST.CODPROD = p_itens(idx).cprod
          AND HIST.CODFILIAL = NFSAIDA.CODFILIAL;
    END IF;

    -- consultando parametro de tributacao por estado
    v_tribuf := consum.tributafraterateado;

    IF v_entrada_saida = 'saida' AND v_tribuf = 'S' THEN
      -- consultando CODST e CODICMTAB do produto
      SELECT CODST, CODICMTAB, CODICMTABPF
        INTO v_codst, v_cmv, v_cmvpf
        FROM PCTRIBUT
        WHERE CODST IN (SELECT CODST
              FROM PCTABTRIB
              WHERE CODPROD = MOV.CODPROD
                AND UFDESTINO = p_ufdestinatario);
    END IF;

    -- GRAVANDO %CMV DO PRODUTO
    MOV.CODICMTAB := v_cmvpf;
    MOV.CODST := v_codst;

    -- GRAVANDO CUSTOS PCMOV
    MOV.CUSTOFIN := ((NVL(MOV.CODICMTAB, 0) / 100) * MOV.PUNIT) + NVL(v_custofin, 0);
    MOV.CUSTOREAL := ((NVL(MOV.CODICMTAB, 0) / 100) * MOV.PUNIT) + NVL(v_custoreal, 0);
    MOV.CUSTOREP := NVL(v_custorep, 0);
    MOV.CUSTOCONT := NVL(v_custocont, 0);
    MOV.CUSTOFINEST := NVL(v_custofin, 0);

    -- GRAVANDO DADOS MOVCOMPLE
    MOVCOMPLE.NUMTRANSITEM := DFSEQ_PCMOVCOMPLE.CURRVAL;
    MOVCOMPLE.DTREGISTRO := MOV.DTMOV;
    MOVCOMPLE.ALIQICMS1RET := NVL(p_itens(idx).pst, 0);

    -- DIFAL
    MOVCOMPLE.VLBASEPARTDEST := NVL(p_itens(idx).vbcufdest, 0);
    MOVCOMPLE.ALIQINTERNADEST := NVL(p_itens(idx).picmsufdest, 0);
    MOVCOMPLE.ALIQINTERORIGPART := NVL(p_itens(idx).picmsinter, 0);
    MOVCOMPLE.PERCPROVPART := NVL(p_itens(idx).picmsinterpart, 0);
    MOVCOMPLE.VLICMSPARTDEST := NVL(p_itens(idx).vicmsufdest, 0) / MOV.QT;
    MOVCOMPLE.VLICMSPARTREM := NVL(p_itens(idx).vicmsufremet, 0) / MOV.QT;

    -- FCP
    MOVCOMPLE.ALIQFCP := NVL(p_itens(idx).pfcpufdest, 0);
    MOVCOMPLE.VLFCPPART := NVL(p_itens(idx).vfcpufdest, 0) / MOV.QT;

    IF p_tpnf = 1 AND NVL(mov.baseicms, 0) > 0 THEN
      -- A gravacao na movimentacao de saida é gravado unitariamente
      mov.baseicms := mov.baseicms / mov.qt;
    END IF;

    -- Validar se produto existe
    SELECT COUNT(*)
      INTO i
      FROM pcprodut
      WHERE codprod = mov.codprod;

    IF i = 0 THEN
      p_retorno := 'Produto XML ' || p_itens(idx).cprod || ' Não cadastrado, Filial: ' || v_codfilial || ' Tipo: ' || v_entrada_saida || ' EAN: ' || p_itens(idx).cEAN;
      ROLLBACK;
      RETURN;
    END IF;

    INSERT INTO pcmov VALUES mov;
    INSERT INTO pcmovcomple VALUES movcomple;

  END LOOP;

  -- =====================================================
  -- ATUALIZAÇÃO DE CUSTOS E PCNFBASE
  -- =====================================================

  dbms_output.put_line('Incluindo custos na tabela pcnfsaid.... ');

  IF v_entrada_saida = 'saida' THEN
    SELECT ROUND(SUM(custofin * qt), 2),
           ROUND(SUM(custoreal * qt), 2),
           ROUND(SUM(custorep * qt), 2),
           ROUND(SUM(custocont * qt), 2)
      INTO v_custofin, v_custoreal, v_custorep, v_custocont
      FROM pcmov
      WHERE pcmov.numtransvenda = nfsaida.numtransvenda;

    UPDATE PCNFSAID
      SET VLCUSTOFIN = v_custofin,
          VLCUSTOREAL = v_custoreal,
          VLCUSTOREP = v_custorep,
          VLCUSTOCONT = v_custocont
      WHERE PCNFSAID.NUMTRANSVENDA = NFSAIDA.NUMTRANSVENDA;

    COMMIT;
  END IF;

  dbms_output.put_line('Incluindo pcnfbase .... ');

  -- PCNFBASE - Agrupar por CFOP
  FOR idx IN 1..p_itens.COUNT LOOP
    -- Verificar se já existe registro para este CFOP
    SELECT COUNT(*)
      INTO i
      FROM PCNFBASE
      WHERE NUMTRANSVENDA = NVL(NFSAIDA.NUMTRANSVENDA, 0)
        AND NUMTRANSENT = NVL(NFENTRADA.NUMTRANSENT, 0)
        AND CODFISCAL = p_itens(idx).cfop;

    IF i = 0 THEN
      INSERT INTO PCNFBASE (
        NUMTRANSVENDA, NUMTRANSENT, CODCONT, CODFISCAL, VLBASE, VLICMS, ALIQUOTA
      )
      VALUES (
        NVL(NFSAIDA.NUMTRANSVENDA, 0),
        NVL(NFENTRADA.NUMTRANSENT, 0),
        NVL(NFSAIDA.CODCONT, NFENTRADA.CODCONT),
        p_itens(idx).cfop,
        NVL(p_itens(idx).vbc, 0),
        NVL(p_itens(idx).vicms, 0),
        NVL(p_itens(idx).picms, 0)
      );
    ELSE
      -- Atualizar valores existentes
      UPDATE PCNFBASE
        SET VLBASE = VLBASE + NVL(p_itens(idx).vbc, 0),
            VLICMS = VLICMS + NVL(p_itens(idx).vicms, 0)
        WHERE NUMTRANSVENDA = NVL(NFSAIDA.NUMTRANSVENDA, 0)
          AND NUMTRANSENT = NVL(NFENTRADA.NUMTRANSENT, 0)
          AND CODFISCAL = p_itens(idx).cfop;
    END IF;
  END LOOP;

  -- =====================================================
  -- ATUALIZAÇÃO DE ESTOQUE
  -- =====================================================

  dbms_output.put_line('Atualizar Saidas do Estoque.... ');

  IF nfsaida.numtransvenda > 0 THEN
    FOR reg IN (SELECT SUM(qtcont) qtde, codprod, codfilial
                FROM pcmov
                WHERE numtransvenda = nfsaida.numtransvenda
                  AND dtcancel IS NULL
                  AND qtcont > 0
                GROUP BY codprod, codfilial)
    LOOP
      UPDATE PCEST
        SET QTEST = NVL(QTEST, 0) - reg.qtde,
            QTESTGER = NVL(QTESTGER, 0) - reg.qtde
        WHERE CODPROD = reg.codprod
          AND CODFILIAL = reg.codfilial;

      UPDATE PCMOV
        SET DTMOVLOG = DTMOV
        WHERE CODPROD = reg.codprod
          AND NUMTRANSVENDA = NFSAIDA.NUMTRANSVENDA;
    END LOOP;
  END IF;

  dbms_output.put_line('--Atualizar Entradas no Estoque');

  IF nfentrada.numtransent > 0 THEN
    FOR reg IN (SELECT SUM(qtcont) qtde, codprod, codfilial
                FROM pcmov
                WHERE numtransent = nfentrada.numtransent
                  AND dtcancel IS NULL
                  AND qtcont > 0
                GROUP BY codprod, codfilial)
    LOOP
      UPDATE PCEST
        SET QTEST = NVL(QTEST, 0) + reg.qtde,
            QTESTGER = NVL(QTESTGER, 0) + reg.qtde
        WHERE CODPROD = reg.codprod
          AND CODFILIAL = reg.codfilial;

      UPDATE PCLOTE
        SET QT = NVL(QT, 0) + reg.qtde
        WHERE CODFILIAL = reg.codfilial
          AND CODPROD = reg.codprod
          AND NUMLOTE = '1';

      UPDATE PCMOV
        SET DTMOVLOG = DTMOV
        WHERE CODPROD = reg.codprod
          AND NUMTRANSENT = NFENTRADA.NUMTRANSENT;
    END LOOP;
  END IF;

  dbms_output.put_line('--Concluido');

  p_retorno := 'Importado com sucesso - Transação Venda: ' ||
               NVL(TO_CHAR(nfsaida.numtransvenda), 'N/A') ||
               ' - Transação Entrada: ' ||
               NVL(TO_CHAR(nfentrada.numtransent), 'N/A');

  COMMIT;

EXCEPTION
  WHEN OTHERS THEN
    ROLLBACK;
    p_retorno := 'Erro: ' || SQLERRM;
    dbms_output.put_line('Erro na procedure: ' || SQLERRM);

END nervsflow_integradora;
/

-- =====================================================
-- EXEMPLO DE USO DA PROCEDURE NERVSFLOW_INTEGRADORA
-- =====================================================

/*
EXEMPLO DE CHAMADA DA PROCEDURE NO N8N:

DECLARE
  v_retorno VARCHAR2(5000);
  v_itens t_itens_nfe;
BEGIN
  -- Criar array de itens
  v_itens := t_itens_nfe();

  -- Adicionar primeiro item
  v_itens.EXTEND;
  v_itens(1) := t_item_nfe(
    1,                    -- nitem
    'PROD001',           -- cprod
    '7891234567890',     -- cean
    'PRODUTO TESTE',     -- xprod
    '12345678',          -- ncm
    5102,                -- cfop
    'UN',                -- ucom
    10,                  -- qcom
    15.50,               -- vuncom
    0,                   -- vdesc
    0,                   -- vfrete
    0,                   -- voutro
    -- Dados ICMS
    '0',                 -- orig
    '000',               -- cst
    18,                  -- picms
    155.00,              -- vbc
    27.90,               -- vicms
    0,                   -- vbcstret
    0,                   -- vicmsstret
    0,                   -- pst
    0,                   -- vicmssubstituto
    0,                   -- vbcst
    0,                   -- picmsst
    0,                   -- pmvast
    0,                   -- vicmsst
    -- Dados PIS
    1.65,                -- ppis
    155.00,              -- vbcpis
    2.56,                -- vpis
    -- Dados COFINS
    7.60,                -- pcofins
    155.00,              -- vbccofins
    11.78,               -- vcofins
    -- Dados IPI
    0,                   -- pipi
    0,                   -- vbcipi
    0,                   -- vipi
    0,                   -- vipidev
    -- Dados DIFAL
    0,                   -- vbcufdest
    0,                   -- picmsufdest
    0,                   -- picmsinter
    0,                   -- picmsinterpart
    0,                   -- vicmsufdest
    0,                   -- vicmsufremet
    0,                   -- pfcpufdest
    0                    -- vfcpufdest
  );

  -- Chamar a procedure
  nervsflow_integradora(
    p_chavenfe            => '35200714200166000187550010000000046271239906',
    p_serie               => '001',
    p_numnota             => 46,
    p_tpnf                => 1,
    p_finnfe              => 1,
    p_tpimp               => 1,
    p_natop               => 'VENDA DE MERCADORIA',
    p_dtemissao           => SYSDATE,
    p_cnpjemitente        => '05330305000185',
    p_ufemitente          => 'SP',
    p_cnpjdestinatario    => '12345678000195',
    p_ufdestinatario      => 'SP',
    p_enderdestinatario   => 'RUA TESTE, 123',
    p_ender_numero        => '123',
    p_bairro_destinatario => 'CENTRO',
    p_xmun                => 'SAO PAULO',
    p_xpais               => 'BRASIL',
    p_cpais               => '1058',
    p_cmun                => '3550308',
    p_cep                 => '01234567',
    p_vltotal             => 155.00,
    p_vlfrete             => 0,
    p_vloutros            => 0,
    p_vst                 => 0,
    p_vbcst               => 0,
    p_vipi                => 0,
    p_vipidev             => 0,
    p_vcofins             => 11.78,
    p_vpis                => 2.56,
    p_itens               => v_itens,
    p_retorno             => v_retorno
  );

  DBMS_OUTPUT.PUT_LINE('Resultado: ' || v_retorno);
END;
/

-- =====================================================
-- DOCUMENTAÇÃO PARA INTEGRAÇÃO COM N8N
-- =====================================================

/*
MAPEAMENTO DOS CAMPOS XML PARA PARÂMETROS DA PROCEDURE:

CABEÇALHO DA NFe:
- p_chavenfe: nfeProc/NFe/infNFe/@Id (remover "NFe")
- p_serie: nfeProc/NFe/infNFe/ide/serie/text()
- p_numnota: nfeProc/NFe/infNFe/ide/nNF/text()
- p_tpnf: nfeProc/NFe/infNFe/ide/tpNF/text()
- p_finnfe: nfeProc/NFe/infNFe/ide/finNFe/text()
- p_tpimp: nfeProc/NFe/infNFe/ide/tpImp/text()
- p_natop: nfeProc/NFe/infNFe/ide/natOp/text()
- p_dtemissao: nfeProc/NFe/infNFe/ide/dhEmi/text() (converter para DATE)

EMITENTE:
- p_cnpjemitente: nfeProc/NFe/infNFe/emit/CNPJ/text()
- p_ufemitente: nfeProc/NFe/infNFe/emit/enderEmit/UF/text()

DESTINATÁRIO:
- p_cnpjdestinatario: nfeProc/NFe/infNFe/dest/CNPJ/text()
- p_ufdestinatario: nfeProc/NFe/infNFe/dest/enderDest/UF/text()
- p_enderdestinatario: nfeProc/NFe/infNFe/dest/enderDest/xLgr/text()
- p_ender_numero: nfeProc/NFe/infNFe/dest/enderDest/nro/text()
- p_bairro_destinatario: nfeProc/NFe/infNFe/dest/enderDest/xBairro/text()
- p_xmun: nfeProc/NFe/infNFe/dest/enderDest/xMun/text()
- p_xpais: nfeProc/NFe/infNFe/dest/enderDest/xPais/text()
- p_cpais: nfeProc/NFe/infNFe/dest/enderDest/cPais/text()
- p_cmun: nfeProc/NFe/infNFe/dest/enderDest/cMun/text()
- p_cep: nfeProc/NFe/infNFe/dest/enderDest/CEP/text()

TOTAIS:
- p_vltotal: nfeProc/NFe/infNFe/total/ICMSTot/vNF/text()
- p_vlfrete: nfeProc/NFe/infNFe/total/ICMSTot/vFrete/text()
- p_vloutros: nfeProc/NFe/infNFe/total/ICMSTot/vOutro/text()
- p_vst: nfeProc/NFe/infNFe/total/ICMSTot/vST/text()
- p_vbcst: nfeProc/NFe/infNFe/total/ICMSTot/vBCST/text()
- p_vipi: nfeProc/NFe/infNFe/total/ICMSTot/vIPI/text()
- p_vipidev: nfeProc/NFe/infNFe/total/ICMSTot/vIPIDevol/text()
- p_vcofins: nfeProc/NFe/infNFe/total/ICMSTot/vCOFINS/text()
- p_vpis: nfeProc/NFe/infNFe/total/ICMSTot/vPIS/text()

ITENS (para cada det):
- nitem: det/@nItem
- cprod: det/prod/cProd/text()
- cean: det/prod/cEAN/text()
- xprod: det/prod/xProd/text()
- ncm: det/prod/NCM/text()
- cfop: det/prod/CFOP/text()
- ucom: det/prod/uCom/text()
- qcom: det/prod/qCom/text()
- vuncom: det/prod/vUnCom/text()
- vdesc: det/prod/vDesc/text()
- vfrete: det/prod/vFrete/text()
- voutro: det/imposto/ICMSUFDest/vOutro/text()

IMPOSTOS POR ITEM:
- orig: det/imposto/ICMS/*/orig/text()
- cst: det/imposto/ICMS/*/CST/text()
- picms: det/imposto/ICMS/*/pICMS/text()
- vbc: det/imposto/ICMS/*/vBC/text()
- vicms: det/imposto/ICMS/*/vICMS/text()
- ... (demais campos de impostos conforme estrutura do XML)

OBSERVAÇÕES:
1. No N8N, use o node "Oracle SQL" para executar a procedure
2. Monte o array de itens iterando sobre os elementos "det" do XML
3. Trate valores nulos/vazios adequadamente
4. Converta datas do formato ISO para DATE do Oracle
5. Remova caracteres não numéricos de campos como CNPJ e CEP
*/

COMMIT;