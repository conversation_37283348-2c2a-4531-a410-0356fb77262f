{"nodes": [{"parameters": {}, "id": "9a45e7ff-8d6e-49bd-9a68-0d6f7493dadd", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-3880, 2820]}, {"parameters": {"setAllData": false, "options": {}}, "id": "c8d2e776-1564-4088-9a0e-f95614ee3cf9", "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [-3580, 2820]}, {"parameters": {"options": {}}, "id": "baa88dcd-8d9e-4bc0-aaf2-38e3909d55d0", "name": "XML", "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-3440, 2820]}, {"parameters": {"fileSelector": "/u01/NERVSFLOW/*.xml"}, "id": "ed54e747-3067-4380-ad5c-4e789827f675", "name": "Ler XML no P:/", "type": "n8n-nodes-base.readBinaryFiles", "typeVersion": 1, "position": [-3720, 2820]}, {"parameters": {"query": "=BEGIN\nUPDATE PCCONSUM\n   SET PROXNUMTRANSVENDA = PROXNUMTRANSVENDA + 1;\nCOMMIT;\nEND;", "limit": null, "options": {"includePreviousData": false}}, "id": "0490df07-08e1-4af7-b6e7-b2207c3fc2b1", "name": "Atualizar a sequencia", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1360, 2380], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "7fe7793f-70e6-4007-9262-cf9928acef9c", "name": "Loop Over Items4", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2180, 2360], "alwaysOutputData": false}, {"parameters": {"jsCode": "// 4-<PERSON><PERSON> de Entrada/<PERSON>a\nlet results = []\n\nfor (const item of items) {\n  const xmlData = item.json // Usar a estrutura de dados do item atual\n\n  if (\n    xmlData &&\n    xmlData['nfeProc'] &&\n    xmlData['nfeProc']['NFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe'] &&\n    xmlData['nfeProc']['NFe']['infNFe']['det']\n  ) {\n    let detList = xmlData['nfeProc']['NFe']['infNFe']['det']\n\n    // Certificar-se de que detList seja sempre um array\n    if (!Array.isArray(detList)) {\n      detList = [detList]\n    }\n    const ICMSTot = xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']\n    const vProdTotal = parseFloat(ICMSTot['vProd'] || 0)\n    const vFrete = parseFloat(ICMSTot['vFrete'] || 0)\n    const BASEICMS = parseFloat(ICMSTot['vBC'] || 0)\n    const vOutro = parseFloat(ICMSTot['vOutro'] || 0) \n\n    for (const det of detList) {\n      if (det['prod']) {\n        const produto = det['prod']\n        const imposto = det['imposto']\n        const pis = imposto && imposto['PIS'] && (imposto['PIS']['PISAliq'] || imposto['PIS']['PISOutr']);\n        const cofins = imposto && imposto['COFINS'] && (imposto['COFINS']['COFINSAliq'] || imposto['COFINS']['COFINSOutr']);\n        const ipi = imposto && imposto['IPI'] && imposto['IPI']['IPITrib']\n        const icms = imposto && imposto['ICMS']\n        const dest = xmlData['nfeProc']['NFe']['infNFe']['dest']\n        // Adicione a variável CODCOB ao resultado, baseado no valor da variável canal\n        const CODCOB = xmlData['nfeProc']['NFe']['infNFe']['ide'][\n          'verProc'\n        ].includes('mercadolivre')\n          ? 'CML'\n          : xmlData['nfeProc']['NFe']['infNFe']['ide']['verProc'].includes(\n              'Bling'\n            )\n          ? 'D2C'\n          : ''\n        // BC de IPI = V. TOTAL PRODUTOS\n        const VLBASEIPI = parseFloat(vProdTotal)\n\n        // Encontrar o primeiro objeto ICMS que existe no XML\n        const icmsObj = [\n          'ICMS00',\n          'ICMS10',\n          'ICMS20',\n          'ICMS30',\n          'ICMS40',\n          'ICMS50',\n          'ICMS60',\n          'ICMS70',\n          'ICMS80',\n          'ICMSSN101',\n          'ICMSSN102',\n          'ICMSSN103',\n          'ICMSSN201',\n          'ICMSSN202',\n          'ICMSSN203',\n          'ICMSSN300',\n          'ICMSSN400',\n          'ICMSSN500',\n          'ICMSSN900'\n        ].find(obj => icms && icms[obj])\n        // Obter o valor da tag CST para PIS ou COFINS\n        const CST_PIS_COFINS = pis \n        ? (pis['CST'] || pis['PISAliq']?.['CST'] || pis['PISOutr']?.['CST'] || '00') \n        : cofins \n        ? (cofins['CST'] || cofins['COFINSAliq']?.['CST'] || cofins['COFINSOutr']?.['CST'] || '00') \n        : '00';\n        // Obter valores das novas tags\n        const xPed = produto['xPed'] || '';\n        const infAdProd = det['infAdProd'] || '';\n        const infCpl = xmlData['nfeProc']['NFe']['infNFe']['infAdic']?.['infCpl'] || '';\n        // Calcular os valores conforme as fórmulas fornecidas\n        const resultItem = {\n          CGCENT: dest['CNPJ'] || dest['CPF'] || null,\n          CONSUMIDORFINAL: dest['CNPJ'] ? 'N' : 'S',\n          CONTRIBUIENTE: dest['CNPJ'] ? 'S' : 'N',\n          TIPOFJ: dest['CNPJ'] ? 'J' : 'F',\n          tpImp: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpImp'],\n          finNFe: xmlData['nfeProc']['NFe']['infNFe']['ide']['finNFe'],\n          modelo: xmlData['nfeProc']['NFe']['infNFe']['ide']['mod'],\n          serie: xmlData['nfeProc']['NFe']['infNFe']['ide']['serie'],\n          nNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['nNF'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          refNFe: [],\n          vFrete: vFrete,\n          vOutro: vOutro, \n          dhEmi: xmlData['nfeProc']['NFe']['infNFe']['ide']['dhEmi'],\n          vNF: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vNF'],\n          vST: xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vST'],\n          pICMS: icms ? parseFloat(icms[icmsObj]?.['pICMS']) || 0 : 0,\n          vICMS:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vICMS'],\n          vProd:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vProd'],\n          BASEICST: parseFloat(\n            ((icms && icms[icmsObj]?.['vBCST']) || 0) / det['prod']['qCom']\n          ),\n          VLBASEIPI: VLBASEIPI,\n\t\t\t\t\t/* Subtrair o vlfreteitem da base de icms para corrigir a mudança da 1400 que esta somando o frete duas vezes no livro fiscal */\n          BASEICMS: parseFloat(BASEICMS - vFrete - vOutro).toFixed(2) || 0, \n          vIPI:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot']['vIPI'] ||\n            0,\n          vIPIDevol:\n            xmlData['nfeProc']['NFe']['infNFe']['total']['ICMSTot'][\n              'vIPIDevol'\n            ] || 0,\n          pIPI: ipi ? ipi['pIPI'] || 0 : 0,\n          vPIS: pis ? pis['vPIS'] || 0 : 0,\n          vCOFINS: cofins ? cofins['vCOFINS'] || 0 : 0,\n          cPais: dest['enderDest']['cPais'],\n          xPais: dest['enderDest']['xPais'],\n          CEP: dest['enderDest']['CEP'],\n          ufDest: dest['enderDest']['UF'],\n          xMun: dest['enderDest']['xMun'],\n          xLgr: dest['enderDest']['xLgr'],\n          nro: dest['enderDest']['nro'],\n          xBairro: dest['enderDest']['xBairro'],\n          chNFe: xmlData['nfeProc']['protNFe']['infProt']['chNFe'],\n          cStat: xmlData['nfeProc']['protNFe']['infProt']['cStat'],\n          nProt: xmlData['nfeProc']['protNFe']['infProt']['nProt'],\n          natOp: xmlData['nfeProc']['NFe']['infNFe']['ide']['natOp'],\n          tpNF: xmlData['nfeProc']['NFe']['infNFe']['ide']['tpNF'],\n          ufEmit: xmlData['nfeProc']['NFe']['infNFe']['emit']['enderEmit']['UF'],\n          CFOP: produto['CFOP'],\n          CGCFILIAL: xmlData['nfeProc']['NFe']['infNFe']['emit']['CNPJ'],\n          CODCOB,\n          CST_PIS_COFINS, \n          xPed,\n          infAdProd,\n          infCpl,\n        }\n        // Adicione refNFe se existir\n        if (\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref'] &&\n          xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n        ) {\n          const refNFeList = Array.isArray(\n            xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n          )\n            ? xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']\n            : [xmlData['nfeProc']['NFe']['infNFe']['ide']['NFref']['refNFe']]\n\n          resultItem.refNFe = refNFeList.map(refNFe => refNFe || '')\n        }\n        // Adicione o resultado ao pairedItem\n        if (!item.pairedItem) {\n          item.pairedItem = {}\n        }\n        item.pairedItem = resultItem\n        // Adicione o resultado ao array 'results'\n        results.push(resultItem)\n      }\n    }\n  }\n}\n\nreturn results\n"}, "id": "aab0e9b9-a7d7-47fa-b1d7-bb17f2080350", "name": "Extrair dados", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3300, 2820]}, {"parameters": {"query": "=SELECT C.PROXNUMTRANSVENDA,\n       CLI.CODCLI,\n       C.CODCONTCLI, \n       C.CODCONTFOR,\n(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFORNEC,\n(SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFILIAL\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE 1 = 1\n    --AND CODUSUR1 = 802 \n    AND APENASNUMEROS(CLI.CGCENT) = '{{ $json[\"previousData\"][\"CGCENT\"] }}'", "limit": 1, "options": {"includePreviousData": true}}, "id": "b591938b-6c3a-419f-8ab3-c0f3b4ebd924", "name": "PROXNUMTRANSVENDA", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2000, 2380], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT S.CODCLI, S.NUMTRANSVENDA, S.CHAVENFE \n  FROM PCNFSAID S\n WHERE CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND S.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "limit": 1, "options": {"includePreviousData": true}}, "id": "0d28d3e4-56a1-4362-b8bd-dc9b8580526b", "name": "Validar Nota", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2520, 2380], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"compare": "<PERSON><PERSON><PERSON>s", "fieldsToCompare": "chNFe", "options": {}}, "id": "7a5dd58a-1d5a-46a5-badd-006128605119", "name": "Remove Duplicates", "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 1, "position": [-3140, 2820]}, {"parameters": {"content": "## Regra Tributaria - finNFe - Finalidade de emissão da NF-e\n\n1 = NF-e normal.\n2 = NF-e complementar.\n3 = NF-e de ajuste.\n4 = Devolução de mercadoria.", "height": 209.7473416040146, "width": 388.85609947320665, "color": 6}, "id": "d89e66d8-1e8d-4109-b848-bdcfbb66ce49", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3860, 2580]}, {"parameters": {"content": "## PATH BASE TESTE\n/u01/xml_mimo/mercado_livre/TESTE/*.xml", "height": 199.87149532710242, "width": 350.64252336448556, "color": 4}, "id": "845a4785-d259-4996-b64a-0bf0bb86a498", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-3440, 2580]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "c69936ca-3699-43ec-a675-55329d08fc9b", "leftValue": "={{ $json.not_found }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "a87a25fe-39a9-4d2e-bfd0-d49a49ad5540", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2360, 2380]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVEND<PERSON>').item.json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "NUMTRANSENT"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "VLBASE", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "VLICMS", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"vICMS\"] }}"}, {"name": "ALIQUOTA", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"pICMS\"] }}"}]}, "options": {}}, "id": "5b1eb2c6-3d35-43d0-9006-00f6bef62b44", "name": "NFBASE", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1520, 2380], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "1c38dfc2-c197-4564-a275-49d5543e7d16", "leftValue": "={{ $json[\"tpNF\"] }}", "rightValue": "1", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}, {"id": "3e91e19f-3200-4ac9-a681-10d8efd2c9e6", "leftValue": "={{ $json.cStat }}", "rightValue": "100", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "id": "6ce3c3f4-ab10-4b8f-bc82-d102a789f881", "name": "Entrada/Saida", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2980, 2820]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "venda", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "id": "038e0481-edcd-4aef-b07b-37cc74f9b281", "name": "natOp", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2740, 2480]}, {"parameters": {"query": "=SELECT S.CODCLI, S.NUMTRANSVENDA, S.CHAVENFE \n  FROM PCNFSAID S\n WHERE CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND S.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSVENDA = S.NUMTRANSVENDA)", "limit": 1, "options": {"includePreviousData": true}}, "id": "c9515005-564e-4e4d-b0fb-ab528b69bf5d", "name": "Validar Nota1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2520, 2580], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c69936ca-3699-43ec-a675-55329d08fc9b", "leftValue": "={{ $json.not_found }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "f9bfb16a-2b17-41f8-a8ae-052bbec11c55", "name": "If1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2360, 2580]}, {"parameters": {"options": {}}, "id": "f4d39b61-17f6-4455-a233-96aacdb698b0", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2180, 2560], "alwaysOutputData": false}, {"parameters": {"query": "=SELECT C.PROXNUMTRANSVENDA,\n       CLI.CODCLI,\n       C.CODCONTCLI, \n       C.CODCONTFOR,\n(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFORNEC,\n(SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFILIAL\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE 1 = 1\n    --AND CODUSUR1 = 802 \n    AND APENASNUMEROS(CLI.CGCENT) = '{{ $json[\"previousData\"][\"CGCENT\"] }}'", "limit": 1, "options": {"includePreviousData": true}}, "id": "89b2af5a-60e8-424e-98de-ca00bc27b0bc", "name": "PROXNUMTRANSVENDA1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2000, 2580], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFSAID", "mode": "name"}, "fields": {"string": [{"name": "TIPOEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"tpImp\"] }}"}, {"name": "FINALIDADENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"finNFe\"] }}"}, {"name": "CAIXA", "value": "0"}, {"name": "SERIE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"serie\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vFrete\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "TIPOVENDA", "value": "SR"}, {"name": "DTSAIDA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTENTREGA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "ICMSRETIDO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vST\"] }}"}, {"name": "BCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vIPI\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "CODPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cPais\"] }}"}, {"name": "DESCPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xPais\"] }}"}, {"name": "CEP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CEP\"] }}"}, {"name": "UF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}"}, {"name": "MUNICIPIO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xMun\"].slice(0,30) }}"}, {"name": "ENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xLgr\"].slice(0,40) }}"}, {"name": "NUMENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nro\"].slice(0,6) }}"}, {"name": "BAIRRO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xBairro\"].slice(0,40) }}"}, {"name": "VLTOTGER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "CHAVENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "PRAZOPONDERADO", "value": "N"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "GERACP", "value": "N"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "EMISSNUMAUTOMATICO", "value": "S"}, {"name": "CTEREGIMEESPECIAL", "value": "N"}, {"name": "NFIPIEMITIDA", "value": "N"}, {"name": "REDUZICMSDOCTE", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODFISCALNF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "DTLANCTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "VLBASEPISCOFINS", "value": "0"}, {"name": "CODCOB", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCOB\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "CONDVENDA", "value": "1"}, {"name": "SITUACAONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cStat\"] }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json[\"CODCLI\"] }}"}, {"name": "CODCLINF", "value": "={{ $json[\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "PROTOCOLONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nProt\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] === $json[\"previousData\"][\"previousData\"][\"ufEmit\"] ? 590 : 690 }}"}, {"name": "CODCONT", "value": "={{ $json[\"CODCONTCLI\"] }}"}, {"name": "CODFORNEC", "value": "={{ $json[\"CODFORNEC\"] }}"}, {"name": "CODUSUR", "value": "802"}, {"name": "NUMVIAS", "value": "1"}, {"name": "CODPRACA", "value": "331"}]}, "options": {}}, "id": "ae1a100f-b215-41e6-981d-04377cda18e6", "name": "INSERT PCNFSAID1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1840, 2580], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=BEGIN\nUPDATE PCCONSUM\n   SET PROXNUMTRANSVENDA = PROXNUMTRANSVENDA + 1;\nCOMMIT;\nEND;", "limit": null, "options": {"includePreviousData": true}}, "id": "1514be30-a2fb-47b5-9d18-4fea4b8adf4b", "name": "Atualizar a sequencia1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1520, 2580], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVENDA1').item.json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "NUMTRANSENT"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('PROXNUMTRANSVENDA1').item.json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "VLBASE", "value": "={{ $('PROXNUMTRANSVENDA1').item.json[\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "VLICMS", "value": "={{ $('PROXNUMTRANSVENDA1').item.json[\"previousData\"][\"previousData\"][\"vICMS\"] }}"}, {"name": "ALIQUOTA", "value": "={{ $('PROXNUMTRANSVENDA1').item.json[\"previousData\"][\"previousData\"][\"pICMS\"] }}"}]}, "options": {}}, "id": "e29d3065-ea9e-4cca-aa11-c45f9a9f4847", "name": "NFBASE1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1680, 2580], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "b0ba1cec-af01-47d8-a0f6-ac551a2fdadf", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "devolucao", "operator": {"type": "string", "operation": "contains"}}, {"id": "2119da2d-2e30-4fd5-aa62-1eed63da3296", "leftValue": "={{ $json.natOp.toLowerCase() }}", "rightValue": "retorno de mercadoria", "operator": {"type": "string", "operation": "contains"}}], "combinator": "or"}, "options": {}}, "id": "19397091-b04c-4b6c-8ce1-e5f50c899da8", "name": "natOp1", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2740, 3200]}, {"parameters": {"options": {}}, "id": "5c4e87f3-8cd2-4c2c-80fc-b781d9b86030", "name": "Loop Over Items7", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2200, 3060], "alwaysOutputData": false}, {"parameters": {"query": "=SELECT N.NUMTRANSVENDA, N.NUMNOTA\n  FROM PCNFSAID N\n WHERE N.CHAVENFE = '{{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }}'", "limit": 1, "options": {"includePreviousData": true}}, "id": "54dc609b-3dcb-4a36-bd7c-0bb5885bd57e", "name": "Pegar Nº NF Venda", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1120, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCCRECLI", "mode": "name"}, "fields": {"string": [{"name": "CODCLI", "value": "={{ $('PROXNUMTRANSENT').item.json[\"CODCLI\"] }}"}, {"name": "DTLANC", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFILIAL", "value": "={{ $('PROXNUMTRANSENT').item.json[\"CODFILIAL\"] }}"}, {"name": "VALOR", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "NUMNOTA", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "CODFUNC", "value": "1"}, {"name": "HISTORICO", "value": "=CREDITO DEVOLUCAO - REF. NF: {{ $json[\"NUMNOTA\"] }}"}, {"name": "CODFUNCLANC", "value": "1"}, {"name": "NUMERARIO", "value": "N"}, {"name": "CODMOVIMENTO", "value": "250004"}, {"name": "CODROTINA", "value": "618"}, {"name": "NUMCRED", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMCRED\"] }}"}]}, "options": {}}, "id": "bc250d74-f873-473a-9c72-4eb40e87784f", "name": "INSERT PCCRECLI", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-940, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCPREST", "mode": "name"}, "set": {"string": [{"name": "VPAGO", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "DTPAG", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODBAIXA", "value": "1"}, {"name": "DTBAIXA", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODCOBORIG", "value": "C"}, {"name": "DTBAIXACRED", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTULTALTER", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "TXPERM", "value": "0"}, {"name": "VALORMULTA", "value": "0"}, {"name": "CODCOB", "value": "CRED"}, {"name": "CODFUNCULTALTER", "value": "1"}]}, "values": {"string": [{"name": "NUMTRANSVENDA", "value": "={{ $('Pegar Nº NF Venda').item.json[\"NUMTRANSVENDA\"] }}"}, {"name": "CODCOB", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"CODCOB\"] }}"}, {"name": "DTPAG"}]}, "options": {}}, "id": "42832454-b06e-478d-ba00-e655446051d2", "name": "Baixar Título com Crédito", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-720, 2920], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "update", "table": {"__rl": true, "value": "PCCRECLI", "mode": "name"}, "set": {"string": [{"name": "DTDESCONTO", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "NUMNOTADESC", "value": "={{ $('Pegar Nº NF Venda').item.json[\"NUMNOTA\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('Pegar Nº NF Venda').item.json[\"NUMTRANSVENDA\"] }}"}]}, "values": {"string": [{"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}]}, "options": {}}, "id": "29ef1f6a-c4dd-4cf1-98c6-3dd742faeb3c", "name": "Baixar Crédito1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-500, 2840], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"content": "## Baixa automática de títulos com crédito de cliente\n**Na rotina 1286 é poss[ivel validar os dados** ", "height": 531.5879709443627, "width": 1033.2629270192563, "color": 4}, "id": "469bfc46-6254-4d05-8fdd-1e51e3dd0f6a", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1160, 2800], "disabled": true}, {"parameters": {"query": "=SELECT C.PROXNUMTRANSENT,\n       CLI.CODCLI,\n       C.CODCONTCLI, \n       C.CODCONTFOR,\n(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFORNEC,\n(SELECT CODIGO FROM PCFILIAL WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFILIAL,\nDFSEQ_PCCRECLI_NUMCRED.NEXTVAL AS PROXNUMCRED\n  FROM PCCLIENT CLI,\n       PCCONSUM C\n  WHERE 1 = 1\n    AND APENASNUMEROS(CLI.CGCENT) = '{{ $json[\"previousData\"][\"CGCENT\"] }}'", "limit": 1, "options": {"includePreviousData": true}}, "id": "8363abda-a1df-4022-bc9e-6510e1c173c5", "name": "PROXNUMTRANSENT", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2020, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "LOGXMLIMPORTADO", "mode": "name"}, "fields": {"string": [{"name": "DATA", "value": "={{ $now.toFormat('dd/MM/yyyy') }} {{ $now.setZone('UTC-3').toFormat('TT') }}"}, {"name": "CHAVEXMLIMPORTACAO", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }}"}, {"name": "OBS", "value": "=XML {{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }} não foi processado"}, {"name": "TABELA", "value": "PCPREST"}]}, "options": {}}, "id": "fce38463-792c-4077-b7f3-0ddf45f06abf", "name": "LOG - NF Saída não processada", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-720, 3140], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "LOGXMLIMPORTADO", "mode": "name"}, "fields": {"string": [{"name": "DATA", "value": "={{ $now.toFormat('dd/MM/yyyy') }} {{ $now.setZone('UTC-3').toFormat('TT') }}"}, {"name": "CHAVEXMLIMPORTACAO", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }}"}, {"name": "OBS", "value": "=Titulo do XML {{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }} não foi lançado"}, {"name": "TABELA", "value": "PCPREST"}]}, "options": {}}, "id": "84fa4997-dba1-429a-834f-bff31a185345", "name": "LOG - Titulo da NF Saída não lançado", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-500, 3020], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"query": "=SELECT C.PROXNUMTRANSENT,\n       F.CODC<PERSON>,\n       C.CODCONTCLI,\n       C.CODCONTFOR,\n       F.CODFORNEC,\n       (SELECT CODIGO\n           FROM PCFILIAL\n           WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"CGCFILIAL\"] }}') AS CODFILIAL\n  FROM PCCONSUM C\n    LEFT JOIN PCFORNEC F\n      ON APENASNUMEROS(F.CGC) = '{{ $json[\"previousData\"][\"CGCENT\"] }}'", "limit": 1, "options": {"includePreviousData": true}}, "id": "1e558a2b-d46d-4494-b45a-83d3d917faa2", "name": "PROXNUMTRANSENT1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2020, 3340], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFENT", "mode": "name"}, "fields": {"string": [{"name": "DTEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "TIPOEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"tpImp\"] }}"}, {"name": "FINALIDADENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"finNFe\"] }}"}, {"name": "SERIE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"serie\"] }}"}, {"name": "NUMTRANSENT", "value": "={{ $json[\"PROXNUMTRANSENT\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vFrete\"] }}"}, {"name": "DTENT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "VLST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vST\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vIPI\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "CODPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cPais\"] }}"}, {"name": "DESCPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xPais\"] }}"}, {"name": "CEP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CEP\"] }}"}, {"name": "UF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}"}, {"name": "MUNICIPIO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xMun\"].slice(0,30) }}"}, {"name": "ENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xLgr\"].slice(0,40) }}"}, {"name": "BAIRRO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xBairro\"].slice(0,40) }}"}, {"name": "VLTOTGER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "CHAVENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "DTLANCTO", "value": "={{ $now.toFormat('dd/MM/yyyy') }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "SITUACAONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cStat\"] }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "PROTOCOLONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nProt\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] === $json[\"previousData\"][\"previousData\"][\"ufEmit\"] ? 112 : 212 }}"}, {"name": "CODCONT", "value": "={{ $json[\"CODCONTFOR\"] }}"}, {"name": "TIPODESCARGA", "value": "R"}, {"name": "CGC", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CGCENT\"] }}"}, {"name": "CODFORNEC", "value": "=(SELECT MIN(CODFORNEC) FROM PCFORNEC WHERE APENASNUMEROS(CGC) = '{{ $json[\"previousData\"][\"previousData\"][\"CGCENT\"] }}')"}, {"name": "MODELO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"modelo\"] }}"}, {"name": "GERANFVENDA", "value": "S"}, {"name": "NUMVIAS", "value": "1"}]}, "options": {}}, "id": "3c1767be-4175-43ac-a8e8-12d5753d1b1d", "name": "INSERT PCNFENT1", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1860, 3340], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c69936ca-3699-43ec-a675-55329d08fc9b", "leftValue": "={{ $json.not_found }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "34c8b21e-9149-40fb-9215-1ed874be09a6", "name": "If2", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2380, 3340]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "c69936ca-3699-43ec-a675-55329d08fc9b", "leftValue": "={{ $json.not_found }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "id": "577215f3-b3e0-4bce-9b06-424e0818e36a", "name": "If3", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-2380, 3080]}, {"parameters": {"query": "=SELECT E.NUMTRANSENT, E.CHAVENFE \n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "limit": 1, "options": {"includePreviousData": true}}, "id": "c9cf3e30-dcc0-4485-bf00-f60489d7d2cf", "name": "Validar Nota2", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2540, 3080], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=BEGIN\nUPDATE PCCONSUM\n   SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;\nCOMMIT;\nEND;", "limit": null, "options": {"includePreviousData": true}}, "id": "bbb6d7e7-8849-4f11-a894-f5ea00905c81", "name": "Atualizar a sequencia2", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1300, 3080], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"options": {}}, "id": "5f80a357-5b66-4c8e-b96e-0aa36164c22c", "name": "Loop Over Items1", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-2200, 3320], "alwaysOutputData": false}, {"parameters": {"query": "=BEGIN\nUPDATE PCCONSUM\n   SET PROXNUMTRANSENT = PROXNUMTRANSENT + 1;\nCOMMIT;\nEND;", "limit": null, "options": {"includePreviousData": true}}, "id": "2aaf9109-d54f-4a68-9aa9-4ae1982dc7e0", "name": "Atualizar a sequencia3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1540, 3340], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=SELECT E.NUMTRANSENT, E.CHAVENFE \n  FROM PCNFENT E\n WHERE E.CHAVENFE = '{{ $json[\"chNFe\"] }}'\n   AND E.DTCANCEL IS NULL\n   AND NOT EXISTS (SELECT 1 FROM PCNFCAN WHERE NUMTRANSENT = E.NUMTRANSENT)", "limit": 1, "options": {"includePreviousData": true}}, "id": "680e14c5-3ca2-4fb9-b2bb-47465c488a63", "name": "Validar Nota3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-2540, 3340], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "VLBASE", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "VLICMS", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"vICMS\"] }}"}, {"name": "ALIQUOTA", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"pICMS\"] }}"}]}, "options": {}}, "id": "f8531dad-e1f0-4701-b8bb-f85cf59e0c09", "name": "NFBASE2", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1660, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFBASE", "mode": "name"}, "fields": {"string": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT1').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "CODCONT", "value": "(SELECT CODCONTCLI FROM PCCONSUM)"}, {"name": "CODFISCAL", "value": "={{ $('PROXNUMTRANSENT1').item.json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "VLBASE", "value": "={{ $('PROXNUMTRANSENT1').item.json[\"previousData\"][\"previousData\"][\"BASEICMS\"] }}"}, {"name": "VLICMS", "value": "={{ $('PROXNUMTRANSENT1').item.json[\"previousData\"][\"previousData\"][\"vICMS\"] }}"}, {"name": "ALIQUOTA", "value": "={{ $('PROXNUMTRANSENT1').item.json[\"previousData\"][\"previousData\"][\"pICMS\"] }}"}]}, "options": {}}, "id": "d6fec66f-3f5c-4c7e-bcfe-fdaa521d77e6", "name": "NFBASE3", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1700, 3340], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"query": "=BEGIN\n  EXECUTE IMMEDIATE '\n    CREATE TABLE LOGXMLIMPORTADO (\n      DATA               DATE,\n      CH<PERSON><PERSON><PERSON><PERSON>PORTACAO VARCHAR2(100 BYTE) NOT NULL,\n      OBS                VARCHAR2(600 BYTE),\n      TABELA             VARCHAR2(40 BYTE),\n      NUMTRANSENT        NUMBER(10, 0),\n      NUMTRANSVENDA      NUMBER(10, 0)\n    )\n    TABLESPACE TS_DADOS\n    STORAGE (INITIAL 64 K NEXT 1 M MAXEXTENTS UNLIMITED)\n    LOGGING\n  ';\n\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.CHAVEXMLIMPORTACAO IS ''Chave de importação do XML''';\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.DATA IS ''Data de cadastro''';\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.OBS IS ''Observações''';\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.TABELA IS ''Nome da tabela''';\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.NUMTRANSENT IS ''Transação de Entrada''';\n  EXECUTE IMMEDIATE 'COMMENT ON COLUMN LOGXMLIMPORTADO.NUMTRANSVENDA IS ''Transação de venda''';\nEND;", "limit": null, "options": {"includePreviousData": false}}, "id": "40bd5e13-fe11-4caf-83da-12027a05fda2", "name": "Criar tabela LOGXMLIMPORTADO", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-300, 3160], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}, "onError": "continueErrorOutput"}, {"parameters": {"content": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n## <PERSON>ras Saídas\n• Notas de remessa não devem aparecer na Rotina 111", "height": 305.9383946896677, "width": 390.31654653035775, "color": 7}, "id": "e4c7aaa7-fd9e-44b2-b68e-a48b16f32617", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1920, 2560]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCESTCOM", "mode": "list", "cachedResultName": "PCESTCOM"}, "fields": {"string": [{"name": "NUMTRANSENT", "value": "={{ $('PROXNUMTRANSENT').item.json[\"PROXNUMTRANSENT\"] }}"}, {"name": "VLDEVOLUCAO", "value": "={{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "VLESTORNO", "value": "0"}, {"name": "DTESTORNO", "value": "={{ $now.toFormat('dd/MM/yyyy') }}"}, {"name": "CODUSUR", "value": "802"}, {"name": "CODFUNC", "value": "1"}, {"name": "NUMTRANSVENDA", "value": "=(SELECT NUMTRANSVENDA FROM PCNFSAID WHERE CHAVENFE = '{{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }}')"}, {"name": "HISTORICO", "value": "MERCADO LIVRE"}, {"name": "VLESTORNOCMV", "value": "=(SELECT VLCUSTOFIN FROM PCNFSAID WHERE CHAVENFE = '{{ $('PROXNUMTRANSENT').item.json[\"previousData\"][\"previousData\"][\"refNFe\"][0] }}')"}, {"name": "CODUSUR2", "value": "0"}, {"name": "CODUSUR3", "value": "0"}, {"name": "CODUSUR4", "value": "0"}, {"name": "VLESTORNO2", "value": "0"}, {"name": "VLESTORNO3", "value": "0"}, {"name": "VLESTORNO4", "value": "0"}]}, "options": {}}, "id": "e5850187-7f03-480d-b2a3-c4609094d371", "name": "PCESTCOM", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1480, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFENT", "mode": "name"}, "fields": {"string": [{"name": "DTEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "TIPOEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"tpImp\"] }}"}, {"name": "FINALIDADENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"finNFe\"] }}"}, {"name": "SERIE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"serie\"] }}"}, {"name": "NUMTRANSENT", "value": "={{ $json[\"PROXNUMTRANSENT\"] }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vFrete\"] }}"}, {"name": "DTENT", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "VLST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vST\"] }}"}, {"name": "BASEICST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vIPI\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "CODPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cPais\"] }}"}, {"name": "DESCPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xPais\"] }}"}, {"name": "CEP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CEP\"] }}"}, {"name": "UF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}"}, {"name": "MUNICIPIO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xMun\"].slice(0,30) }}"}, {"name": "ENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xLgr\"].slice(0,40) }}"}, {"name": "BAIRRO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xBairro\"].slice(0,40) }}"}, {"name": "VLTOTGER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "CHAVENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "DTLANCTO", "value": "={{ $now.toFormat('dd/MM/yyyy') }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "SITUACAONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cStat\"] }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "CODFILIAL", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $json[\"CODFILIAL\"] }}"}, {"name": "PROTOCOLONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nProt\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] === $json[\"previousData\"][\"previousData\"][\"ufEmit\"] ? 132 : 232 }}"}, {"name": "CODCONT", "value": "={{ $json[\"CODCONTFOR\"] }}"}, {"name": "TIPODESCARGA", "value": "6"}, {"name": "CGC", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CGCENT\"] }}"}, {"name": "CODFORNECNF", "value": "=(SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"previousData\"][\"previousData\"][\"CGCENT\"] }}')"}, {"name": "CODFORNEC", "value": "=(SELECT MIN(CODCLI) FROM PCCLIENT WHERE APENASNUMEROS(CGCENT) = '{{ $json[\"previousData\"][\"previousData\"][\"CGCENT\"] }}')"}, {"name": "GERANFDEVCLI", "value": "S"}, {"name": "MODELO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"modelo\"] }}"}, {"name": "CODDEVOL", "value": "340"}, {"name": "EMISSAOPROPRIA", "value": "N"}, {"name": "CODUSURDEVOL", "value": "802"}, {"name": "NUMVIAS", "value": "1"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}]}, "options": {}}, "id": "365107d5-fc9f-418b-8b6c-0e26468f5b0a", "name": "INSERT PCNFENT", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1840, 3080], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"content": "## Financeiro\n- Os títulos são baixados em lote pelo financeiro via **Rotina 1207** \n\n- Add NUMCAR = 0 na PCPREST para que os títulos apareçam na rotina 410.", "width": 314.432338363199}, "id": "d478797f-72e9-4844-9af2-5dabc7c52568", "name": "Sticky Note5", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1680, 2200]}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCNFSAID", "mode": "name"}, "fields": {"string": [{"name": "TIPOEMISSAO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"tpImp\"] }}"}, {"name": "FINALIDADENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"finNFe\"] }}"}, {"name": "CAIXA", "value": "0"}, {"name": "SERIE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"serie\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $json.PROXNUMTRANSVENDA }}"}, {"name": "NUMNOTA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "VLFRETE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vFrete\"] }}"}, {"name": "VLOUTRASDESP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vOutro\"] }}"}, {"name": "TIPOVENDA", "value": "1"}, {"name": "DTSAIDA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTENTREGA", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLTOTAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "ICMSRETIDO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vST\"] }}"}, {"name": "BCST", "value": "={{ $json[\"previousData\"][\"previousData\"][\"BASEICST\"] }}"}, {"name": "VLIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vIPI\"] }}"}, {"name": "VLPIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vPIS\"] }}"}, {"name": "VLCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vCOFINS\"] }}"}, {"name": "CODPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cPais\"] }}"}, {"name": "DESCPAIS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xPais\"] }}"}, {"name": "CEP", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CEP\"] }}"}, {"name": "UF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] }}"}, {"name": "MUNICIPIO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xMun\"].slice(0,30) }}"}, {"name": "ENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xLgr\"].slice(0,40) }}"}, {"name": "NUMENDERECO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nro\"].slice(0,6) }}"}, {"name": "BAIRRO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"xBairro\"].slice(0,40) }}"}, {"name": "VLTOTGER", "value": "={{ $json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "CHAVENFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"chNFe\"] }}"}, {"name": "PRAZOPONDERADO", "value": "N"}, {"name": "PERBASEREDOUTRASDESP", "value": "0"}, {"name": "GERACP", "value": "N"}, {"name": "CODFISCALFRETE", "value": "0"}, {"name": "PERCICMFRETE", "value": "0"}, {"name": "AMBIENTENFE", "value": "P"}, {"name": "CONFERIDO", "value": "N"}, {"name": "AGREGASTVLMERC", "value": "N"}, {"name": "EMISSNUMAUTOMATICO", "value": "N"}, {"name": "CTEREGIMEESPECIAL", "value": "N"}, {"name": "NFIPIEMITIDA", "value": "N"}, {"name": "REDUZICMSDOCTE", "value": "N"}, {"name": "GERARBCRNFE", "value": "S"}, {"name": "ALIQICMOUTRASDESP", "value": "0"}, {"name": "CODFISCALOUTRASDESP", "value": "0"}, {"name": "CODSITTRIBPISCOFINS", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CST_PIS_COFINS\"] }}"}, {"name": "CODFISCALNF", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CFOP\"] }}"}, {"name": "DTLANCTO", "value": "={{ $json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "VLBASEIPI", "value": "={{ $json[\"previousData\"][\"previousData\"][\"VLBASEIPI\"] }}"}, {"name": "PERPIS", "value": "0"}, {"name": "PERCOFINS", "value": "0"}, {"name": "VLBASEPISCOFINS", "value": "0"}, {"name": "CODCOB", "value": "={{ $json[\"previousData\"][\"previousData\"][\"CODCOB\"] }}"}, {"name": "CODPLPAG", "value": "1"}, {"name": "CONDVENDA", "value": "1"}, {"name": "SITUACAONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"cStat\"] }}"}, {"name": "ESPECIE", "value": "NF"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODCLI", "value": "={{ $json.CODCLI }}"}, {"name": "CODCLINF", "value": "={{ $json.CODCLI }}"}, {"name": "CODFILIAL", "value": "={{ $json.CODFILIAL }}"}, {"name": "CODFILIALNF", "value": "={{ $json.CODFILIAL }}"}, {"name": "PROTOCOLONFE", "value": "={{ $json[\"previousData\"][\"previousData\"][\"nProt\"] }}"}, {"name": "CODFISCAL", "value": "={{ $json[\"previousData\"][\"previousData\"][\"ufDest\"] === $json[\"previousData\"][\"previousData\"][\"ufEmit\"] ? 599 : 699 }}"}, {"name": "CODCONT", "value": "={{ $json.CODCONTCLI }}"}, {"name": "CODFORNEC", "value": "={{ $json.CODFORNEC }}"}, {"name": "CODUSUR", "value": "802"}, {"name": "NUMVIAS", "value": "1"}, {"name": "CODPRACA", "value": "331"}, {"name": "CODSUPERVISOR", "value": "9"}]}, "options": {}}, "id": "e5e36d1e-d460-409b-ae6b-cb06f9a434cc", "name": "PCNFSAID", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1840, 2380], "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}, {"parameters": {"operation": "insert", "table": {"__rl": true, "value": "PCPREST", "mode": "name"}, "fields": {"string": [{"name": "CODCLI", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"CODCLI\"] }}"}, {"name": "CODFILIAL", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"CODFILIAL\"] }}"}, {"name": "CODFILIALNF", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"CODFILIAL\"] }}"}, {"name": "NUMTRANSVENDA", "value": "={{ $('PROXNUMTRANSVEND<PERSON>').item.json[\"PROXNUMTRANSVENDA\"] }}"}, {"name": "VALOR", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "PREST", "value": "1"}, {"name": "DUPLIC", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"nNF\"] }}"}, {"name": "DTVENC", "value": "={{ new Date(new Date($now).getTime() + 45 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR') }}"}, {"name": "DTVENCORIG", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTEMISSAO", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODCOB", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"CODCOB\"] }}"}, {"name": "CODCOBORIG", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"CODCOB\"] }}"}, {"name": "STATUS", "value": "A"}, {"name": "VALORORIG", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"vNF\"] }}"}, {"name": "TXPERM", "value": "0"}, {"name": "OPERACAO", "value": "S"}, {"name": "CODUSUR", "value": "802"}, {"name": "NUMCAR", "value": "0"}, {"name": "CODFUNCCXMOT", "value": "1"}, {"name": "DTTRANSACAOCC", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTCXMOT", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "DTCXMOTHHMMSS", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') + \" \" +   $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[1].split('-')[0] }}"}, {"name": "DTULTALTER", "value": "={{ $('PROXNUMTRANSVENDA').item.json[\"previousData\"][\"previousData\"][\"dhEmi\"].split('T')[0].split('-').reverse().join('/') }}"}, {"name": "CODFUNCULTALTER", "value": "1"}, {"name": "TIPOOPERACAOTEF", "value": "C"}, {"name": "NUMTRANS", "value": "={{ $('PROXNUMTRANSVENDA').item.json.PROXNUMTRANSVENDA }}"}]}, "options": {}}, "id": "931beff3-3228-46df-811d-f02aafa8da4b", "name": "PCPREST", "type": "CUSTOM.oracleSql", "typeVersion": 1, "position": [-1680, 2380], "alwaysOutputData": true, "credentials": {"oracleSqlApi": {"id": "pkIOPWJz38ihjZMj", "name": "Oracle - PROD"}}}], "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Ler XML no P:/", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Extrair dados", "type": "main", "index": 0}]]}, "Ler XML no P:/": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Atualizar a sequencia": {"main": [[{"node": "Loop Over Items4", "type": "main", "index": 0}]]}, "Loop Over Items4": {"main": [[], [{"node": "PROXNUMTRANSVENDA", "type": "main", "index": 0}]]}, "Extrair dados": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "PROXNUMTRANSVENDA": {"main": [[{"node": "PCNFSAID", "type": "main", "index": 0}]]}, "Validar Nota": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Entrada/Saida", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Loop Over Items4", "type": "main", "index": 0}]]}, "NFBASE": {"main": [[{"node": "Atualizar a sequencia", "type": "main", "index": 0}]]}, "Entrada/Saida": {"main": [[{"node": "natOp", "type": "main", "index": 0}], [{"node": "natOp1", "type": "main", "index": 0}]]}, "natOp": {"main": [[{"node": "Validar Nota", "type": "main", "index": 0}], [{"node": "Validar Nota1", "type": "main", "index": 0}]]}, "Validar Nota1": {"main": [[{"node": "If1", "type": "main", "index": 0}]]}, "If1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "PROXNUMTRANSVENDA1", "type": "main", "index": 0}]]}, "PROXNUMTRANSVENDA1": {"main": [[{"node": "INSERT PCNFSAID1", "type": "main", "index": 0}]]}, "INSERT PCNFSAID1": {"main": [[{"node": "NFBASE1", "type": "main", "index": 0}]]}, "Atualizar a sequencia1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "NFBASE1": {"main": [[{"node": "Atualizar a sequencia1", "type": "main", "index": 0}]]}, "natOp1": {"main": [[{"node": "Validar Nota2", "type": "main", "index": 0}], [{"node": "Validar Nota3", "type": "main", "index": 0}]]}, "Loop Over Items7": {"main": [[], [{"node": "PROXNUMTRANSENT", "type": "main", "index": 0}]]}, "Pegar Nº NF Venda": {"main": [[{"node": "INSERT PCCRECLI", "type": "main", "index": 0}]]}, "INSERT PCCRECLI": {"main": [[{"node": "Baixar Título com Crédito", "type": "main", "index": 0}], [{"node": "LOG - NF Saída não processada", "type": "main", "index": 0}]]}, "Baixar Título com Crédito": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}, {"node": "Baixar Crédito1", "type": "main", "index": 0}], [{"node": "LOG - Titulo da NF Saída não lançado", "type": "main", "index": 0}]]}, "PROXNUMTRANSENT": {"main": [[{"node": "INSERT PCNFENT", "type": "main", "index": 0}]]}, "LOG - NF Saída não processada": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}], [{"node": "Criar tabela LOGXMLIMPORTADO", "type": "main", "index": 0}]]}, "LOG - Titulo da NF Saída não lançado": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}], [{"node": "Criar tabela LOGXMLIMPORTADO", "type": "main", "index": 0}]]}, "PROXNUMTRANSENT1": {"main": [[{"node": "INSERT PCNFENT1", "type": "main", "index": 0}]]}, "INSERT PCNFENT1": {"main": [[{"node": "NFBASE3", "type": "main", "index": 0}]]}, "If2": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "If3": {"main": [[{"node": "Loop Over Items7", "type": "main", "index": 0}]]}, "Validar Nota2": {"main": [[{"node": "If3", "type": "main", "index": 0}]]}, "Atualizar a sequencia2": {"main": [[{"node": "Pegar Nº NF Venda", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[], [{"node": "PROXNUMTRANSENT1", "type": "main", "index": 0}]]}, "Atualizar a sequencia3": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Validar Nota3": {"main": [[{"node": "If2", "type": "main", "index": 0}]]}, "NFBASE2": {"main": [[{"node": "PCESTCOM", "type": "main", "index": 0}]]}, "NFBASE3": {"main": [[{"node": "Atualizar a sequencia3", "type": "main", "index": 0}]]}, "Criar tabela LOGXMLIMPORTADO": {"main": [[{"node": "LOG - NF Saída não processada", "type": "main", "index": 0}, {"node": "LOG - Titulo da NF Saída não lançado", "type": "main", "index": 0}]]}, "PCESTCOM": {"main": [[{"node": "Atualizar a sequencia2", "type": "main", "index": 0}]]}, "INSERT PCNFENT": {"main": [[{"node": "NFBASE2", "type": "main", "index": 0}]]}, "PCNFSAID": {"main": [[{"node": "PCPREST", "type": "main", "index": 0}]]}, "PCPREST": {"main": [[{"node": "NFBASE", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "5fab609e97caf85a95cad132b5c4a20271d41f64fb86f9c1bf9200f21d684f36"}}